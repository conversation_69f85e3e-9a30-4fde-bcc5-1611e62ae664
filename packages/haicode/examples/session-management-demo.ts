#!/usr/bin/env tsx

/**
 * 会话管理功能演示
 * 展示如何使用 HaiCodeAgent 的会话持久存储和管理功能
 */

import { HaiCodeAgent, type SessionInfo, type SessionDetails } from '../src/index.js';

async function sessionManagementDemo() {
  console.log('🎯 HaiCode Agent 会话管理功能演示\n');

  // 创建启用 MongoDB 持久存储的代理
  const agent = new HaiCodeAgent({
    enableMongoPersistence: true,
    systemPrompt: '你是一个智能助手，能够记住我们之前的对话内容。',
    model: 'ht::saas-deepseek-v3',
    baseUrl: process.env.OPENAI_BASE_URL,
    apiKey: process.env.OPENAI_API_KEY,
  });

  try {
    console.log('📋 1. 获取最近的会话列表');
    console.log('=' .repeat(50));
    
    const recentSessions = await agent.getRecentSessions(10);
    
    if (recentSessions.length === 0) {
      console.log('📭 暂无历史会话，让我们创建一些...\n');
      
      // 创建几个示例会话
      await createSampleSessions(agent);
      
      // 重新获取会话列表
      const newSessions = await agent.getRecentSessions(10);
      displaySessionList(newSessions);
    } else {
      displaySessionList(recentSessions);
    }

    console.log('\n📖 2. 查看会话详情');
    console.log('=' .repeat(50));
    
    const sessionsToShow = await agent.getRecentSessions(3);
    if (sessionsToShow.length > 0) {
      const sessionDetails = await agent.getSessionDetails(sessionsToShow[0].sessionId);
      if (sessionDetails) {
        displaySessionDetails(sessionDetails);
      }
    }

    console.log('\n💬 3. 继续现有会话');
    console.log('=' .repeat(50));
    
    if (sessionsToShow.length > 0) {
      const sessionId = sessionsToShow[0].sessionId;
      console.log(`🔄 继续会话: ${sessionId}`);
      
      const response = await agent.processMessage(
        '请总结一下我们之前的对话内容。', 
        sessionId
      );
      
      console.log('🤖 Agent 回复:');
      console.log(response);
    }

    console.log('\n✅ 会话管理演示完成！');

  } catch (error) {
    console.error('❌ 演示过程中出错:', error);
  } finally {
    await agent.close();
  }
}

/**
 * 创建一些示例会话
 */
async function createSampleSessions(agent: HaiCodeAgent) {
  console.log('🔨 创建示例会话...\n');

  // 会话1: 编程相关
  const session1 = `demo-coding-${Date.now()}`;
  await agent.processMessage('你好，我想学习 TypeScript，能给我一些建议吗？', session1);
  await agent.processMessage('什么是泛型？能举个例子吗？', session1);
  
  // 等待一下
  await new Promise(resolve => setTimeout(resolve, 1000));

  // 会话2: 日常对话
  const session2 = `demo-chat-${Date.now()}`;
  await agent.processMessage('今天天气不错，你觉得呢？', session2);
  await agent.processMessage('推荐一些好看的电影吧。', session2);
  
  // 等待一下
  await new Promise(resolve => setTimeout(resolve, 1000));

  // 会话3: 技术问题
  const session3 = `demo-tech-${Date.now()}`;
  await agent.processMessage('如何优化 Node.js 应用的性能？', session3);
  
  console.log('✅ 示例会话创建完成\n');
}

/**
 * 显示会话列表
 */
function displaySessionList(sessions: SessionInfo[]) {
  console.log(`📋 找到 ${sessions.length} 个会话:\n`);
  
  sessions.forEach((session, index) => {
    console.log(`${index + 1}. 会话 ID: ${session.sessionId}`);
    console.log(`   📅 创建时间: ${session.createdAt.toLocaleString()}`);
    console.log(`   🔄 更新时间: ${session.updatedAt.toLocaleString()}`);
    console.log(`   💬 消息数量: ${session.messageCount}`);
    console.log(`   📝 最后消息: ${session.lastMessage || '无'}`);
    console.log('');
  });
}

/**
 * 显示会话详情
 */
function displaySessionDetails(details: SessionDetails) {
  console.log(`📖 会话详情: ${details.sessionId}\n`);
  console.log(`📅 创建时间: ${details.createdAt.toLocaleString()}`);
  console.log(`🔄 更新时间: ${details.updatedAt.toLocaleString()}`);
  console.log(`💬 消息总数: ${details.messageCount}\n`);
  
  console.log('📜 对话历史:');
  console.log('-'.repeat(40));
  
  details.messages.forEach((message, index) => {
    const role = message.constructor.name === 'HumanMessage' ? '👤 用户' : '🤖 助手';
    const content = typeof message.content === 'string' 
      ? message.content 
      : JSON.stringify(message.content);
    
    // 限制显示长度
    const displayContent = content.length > 150 
      ? content.substring(0, 150) + '...' 
      : content;
    
    console.log(`${index + 1}. ${role}:`);
    console.log(`   ${displayContent}\n`);
  });
}

/**
 * 交互式会话管理
 */
async function interactiveSessionManagement() {
  console.log('🎮 交互式会话管理\n');
  
  const agent = new HaiCodeAgent({
    enableMongoPersistence: true,
    systemPrompt: '你是一个智能助手。',
  });

  try {
    // 这里可以添加交互式逻辑
    // 比如让用户选择要查看的会话等
    console.log('💡 提示: 这里可以扩展为交互式界面');
    
  } finally {
    await agent.close();
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--interactive')) {
    await interactiveSessionManagement();
  } else {
    await sessionManagementDemo();
  }
}

// 如果直接运行此文件
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { sessionManagementDemo, interactiveSessionManagement };
