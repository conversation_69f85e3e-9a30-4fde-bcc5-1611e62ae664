#!/usr/bin/env tsx

/**
 * MongoDB 会话持久存储测试
 * 测试 HaiCodeAgent 的 MongoDB 持久存储功能
 */

import { HaiCodeAgent } from '../src/index.js';

async function testMongoDBPersistence() {
  console.log('🧪 MongoDB 会话持久存储测试...\n');

  // 创建启用 MongoDB 持久存储的代理实例
  const agent = new HaiCodeAgent({
    enableMongoPersistence: true,
    systemPrompt: '你是一个有用的助手，请记住我们的对话历史。',
    model: 'ht::saas-deepseek-v3',
    baseUrl: process.env.OPENAI_BASE_URL,
    apiKey: process.env.OPENAI_API_KEY,
  });

  try {
    console.log('📝 测试1: 创建新会话...');
    const sessionId1 = `test-session-${Date.now()}-1`;
    
    // 第一轮对话
    console.log(`\n🔄 会话 ${sessionId1} - 第一轮对话:`);
    const response1 = await agent.processMessage('你好，我叫张三，请记住我的名字。', sessionId1);
    console.log('🤖 Agent:', response1.substring(0, 200) + (response1.length > 200 ? '...' : ''));

    // 第二轮对话
    console.log(`\n🔄 会话 ${sessionId1} - 第二轮对话:`);
    const response2 = await agent.processMessage('我的名字是什么？', sessionId1);
    console.log('🤖 Agent:', response2.substring(0, 200) + (response2.length > 200 ? '...' : ''));

    console.log('\n📝 测试2: 创建另一个会话...');
    const sessionId2 = `test-session-${Date.now()}-2`;
    
    // 另一个会话的对话
    console.log(`\n🔄 会话 ${sessionId2} - 对话:`);
    const response3 = await agent.processMessage('你好，我叫李四。我的名字是什么？', sessionId2);
    console.log('🤖 Agent:', response3.substring(0, 200) + (response3.length > 200 ? '...' : ''));

    // 等待一下确保数据已保存
    await new Promise(resolve => setTimeout(resolve, 2000));

    console.log('\n📝 测试3: 获取最近的会话列表...');
    const recentSessions = await agent.getRecentSessions(5);
    console.log(`📋 找到 ${recentSessions.length} 个最近的会话:`);
    
    for (const session of recentSessions) {
      console.log(`  - 会话ID: ${session.sessionId}`);
      console.log(`    创建时间: ${session.createdAt.toLocaleString()}`);
      console.log(`    更新时间: ${session.updatedAt.toLocaleString()}`);
      console.log(`    消息数量: ${session.messageCount}`);
      console.log(`    最后消息: ${session.lastMessage || '无'}`);
      console.log('');
    }

    console.log('\n📝 测试4: 获取会话详情...');
    if (recentSessions.length > 0) {
      const sessionDetails = await agent.getSessionDetails(recentSessions[0].sessionId);
      if (sessionDetails) {
        console.log(`📖 会话 ${sessionDetails.sessionId} 的详情:`);
        console.log(`  创建时间: ${sessionDetails.createdAt.toLocaleString()}`);
        console.log(`  更新时间: ${sessionDetails.updatedAt.toLocaleString()}`);
        console.log(`  消息总数: ${sessionDetails.messageCount}`);
        console.log('  消息历史:');
        
        for (let i = 0; i < sessionDetails.messages.length; i++) {
          const msg = sessionDetails.messages[i];
          const role = msg.constructor.name === 'HumanMessage' ? '👤 用户' : '🤖 助手';
          const content = typeof msg.content === 'string' 
            ? msg.content.substring(0, 100) + (msg.content.length > 100 ? '...' : '')
            : JSON.stringify(msg.content).substring(0, 100) + '...';
          console.log(`    ${i + 1}. ${role}: ${content}`);
        }
      } else {
        console.log('❌ 无法获取会话详情');
      }
    }

    console.log('\n📝 测试5: 验证会话持久性...');
    console.log(`\n🔄 重新连接到会话 ${sessionId1}:`);
    const response4 = await agent.processMessage('再次确认，我的名字是什么？', sessionId1);
    console.log('🤖 Agent:', response4.substring(0, 200) + (response4.length > 200 ? '...' : ''));

    console.log('\n✅ MongoDB 会话持久存储测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await agent.close();
  }
}

async function testFallbackToMemory() {
  console.log('\n🧪 测试回退到内存存储...\n');

  // 创建一个使用错误 MongoDB 配置的代理，应该回退到内存存储
  const agent = new HaiCodeAgent({
    enableMongoPersistence: true, // 启用但会失败
    systemPrompt: '你是一个测试助手。',
  });

  try {
    console.log('📝 测试内存存储回退...');
    const sessionId = `fallback-test-${Date.now()}`;
    
    const response = await agent.processMessage('这是一个测试消息', sessionId);
    console.log('🤖 Agent:', response.substring(0, 100) + (response.length > 100 ? '...' : ''));
    
    // 尝试获取会话列表（应该为空，因为使用的是内存存储）
    const sessions = await agent.getRecentSessions();
    console.log(`📋 内存存储模式下的会话数量: ${sessions.length}`);
    
    console.log('✅ 回退测试完成');
    
  } catch (error) {
    console.error('❌ 回退测试失败:', error);
  } finally {
    await agent.close();
  }
}

// 运行测试
async function runTests() {
  console.log('🚀 开始 MongoDB 会话持久存储测试\n');
  
  try {
    await testMongoDBPersistence();
    await testFallbackToMemory();
  } catch (error) {
    console.error('❌ 测试套件失败:', error);
  }
  
  console.log('\n🏁 所有测试完成');
}

// 如果直接运行此文件
if (import.meta.url === `file://${process.argv[1]}`) {
  runTests().catch(console.error);
}

export { testMongoDBPersistence, testFallbackToMemory };
