#!/usr/bin/env tsx

/**
 * Sessions Command Test
 * 测试 /sessions 命令的各项功能
 */

import { HaiCodeAgent } from '../src/index.js';
import { getUserInfo } from '../src/utils/user.js';
import { getVersion } from '../src/utils/version.js';

async function testSessionsCommand() {
  console.log('🧪 测试 /sessions 命令功能...\n');

  // 创建代理实例，启用 MongoDB 持久化
  const agent = new HaiCodeAgent({
    model: 'ht::saas-deepseek-v3',
    enableMongoPersistence: true,
    langfuse: {
      enabled: false, // 测试时禁用 Langfuse
      userId: getUserInfo().userName,
      sessionId: 'test-session',
      version: getVersion(),
    },
  });

  try {
    console.log('📝 创建测试会话...');
    
    // 创建几个测试会话
    const testSessions = [
      'test-session-1',
      'test-session-2', 
      'test-session-3'
    ];

    for (const sessionId of testSessions) {
      console.log(`  创建会话: ${sessionId}`);
      
      // 在每个会话中发送一些消息
      for await (const chunk of agent.streamMessage('你好，这是测试消息', sessionId)) {
        // 消费流但不输出
      }
      
      for await (const chunk of agent.streamMessage('请记住我们的对话', sessionId)) {
        // 消费流但不输出
      }
    }

    console.log('\n✅ 测试会话创建完成\n');

    // 测试 /sessions 命令（默认列表）
    console.log('🔍 测试 /sessions 命令（默认列表）:');
    for await (const chunk of agent.streamMessage('/sessions', 'test-session-1')) {
      process.stdout.write(chunk);
    }
    console.log('\n');

    // 测试 /sessions list 命令
    console.log('🔍 测试 /sessions list 命令:');
    for await (const chunk of agent.streamMessage('/sessions list 5', 'test-session-1')) {
      process.stdout.write(chunk);
    }
    console.log('\n');

    // 测试 /sessions current 命令
    console.log('🔍 测试 /sessions current 命令:');
    for await (const chunk of agent.streamMessage('/sessions current', 'test-session-1')) {
      process.stdout.write(chunk);
    }
    console.log('\n');

    // 测试 /sessions details 命令
    console.log('🔍 测试 /sessions details 命令:');
    for await (const chunk of agent.streamMessage('/sessions details test-session-2', 'test-session-1')) {
      process.stdout.write(chunk);
    }
    console.log('\n');

    // 测试 /sessions select 命令
    console.log('🔍 测试 /sessions select 命令:');
    for await (const chunk of agent.streamMessage('/sessions select test-session-2', 'test-session-1')) {
      process.stdout.write(chunk);
    }
    console.log('\n');

    // 验证会话切换是否生效
    const currentSessionId = agent.getCurrentSessionId();
    console.log(`🔄 当前会话ID: ${currentSessionId}`);
    
    if (currentSessionId === 'test-session-2') {
      console.log('✅ 会话切换成功！');
    } else {
      console.log('❌ 会话切换失败');
    }

    // 测试部分匹配
    console.log('\n🔍 测试部分匹配 /sessions select:');
    for await (const chunk of agent.streamMessage('/sessions select test-session-3', 'test-session-2')) {
      process.stdout.write(chunk);
    }
    console.log('\n');

    // 测试错误情况
    console.log('🔍 测试不存在的会话:');
    for await (const chunk of agent.streamMessage('/sessions select nonexistent', 'test-session-3')) {
      process.stdout.write(chunk);
    }
    console.log('\n');

    // 测试缺少参数的情况
    console.log('🔍 测试缺少参数:');
    for await (const chunk of agent.streamMessage('/sessions select', 'test-session-3')) {
      process.stdout.write(chunk);
    }
    console.log('\n');

    // 测试帮助命令
    console.log('🔍 测试帮助命令:');
    for await (const chunk of agent.streamMessage('/help sessions', 'test-session-3')) {
      process.stdout.write(chunk);
    }
    console.log('\n');

    console.log('✅ 所有测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await agent.close();
  }
}

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  testSessionsCommand().catch(console.error);
}

export { testSessionsCommand };
