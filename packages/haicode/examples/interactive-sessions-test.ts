#!/usr/bin/env tsx

/**
 * Interactive Sessions Test
 * 交互式会话管理测试
 */

import { HaiCodeAgent } from '../src/index.js';
import { runInteractiveMode } from '../src/utils/interactive.js';
import { getUserInfo } from '../src/utils/user.js';
import { getVersion } from '../src/utils/version.js';
import { getUUID } from '../src/utils/uuid.js';

async function setupTestSessions() {
  console.log('🔧 设置测试会话...');
  
  const agent = new HaiCodeAgent({
    model: 'ht::saas-deepseek-v3',
    enableMongoPersistence: true,
    langfuse: {
      enabled: false,
      userId: getUserInfo().userName,
      sessionId: 'setup-session',
      version: getVersion(),
    },
  });

  try {
    // 创建几个测试会话
    const testSessions = [
      { id: 'demo-session-1', messages: ['你好，我是第一个会话', '请帮我写一个 Python 函数'] },
      { id: 'demo-session-2', messages: ['Hello, this is session 2', '请解释什么是机器学习'] },
      { id: 'demo-session-3', messages: ['这是第三个会话', '请帮我分析这段代码'] },
    ];

    for (const session of testSessions) {
      console.log(`  创建会话: ${session.id}`);
      
      for (const message of session.messages) {
        // 发送消息但不输出响应
        for await (const chunk of agent.streamMessage(message, session.id)) {
          // 消费流但不输出
        }
      }
    }

    console.log('✅ 测试会话设置完成\n');
  } catch (error) {
    console.error('❌ 设置测试会话失败:', error);
  } finally {
    await agent.close();
  }
}

async function startInteractiveTest() {
  console.log('🚀 启动交互式会话管理测试\n');
  
  // 先设置测试会话
  await setupTestSessions();
  
  console.log('📋 可用的测试命令:');
  console.log('  /sessions              - 查看会话列表');
  console.log('  /sessions list         - 查看会话列表');
  console.log('  /sessions current      - 查看当前会话');
  console.log('  /sessions select demo  - 切换到以 "demo" 开头的会话');
  console.log('  /sessions details demo - 查看会话详情');
  console.log('  /help sessions         - 查看帮助');
  console.log('  exit                   - 退出\n');

  // 创建新的代理实例用于交互
  const agent = new HaiCodeAgent({
    model: 'ht::saas-deepseek-v3',
    enableMongoPersistence: true,
    langfuse: {
      enabled: false,
      userId: getUserInfo().userName,
      sessionId: getUUID(),
      version: getVersion(),
    },
  });

  // 启动交互模式
  const initialSessionId = getUUID();
  await runInteractiveMode(agent, initialSessionId);
}

// 运行交互式测试
if (import.meta.url === `file://${process.argv[1]}`) {
  startInteractiveTest().catch(console.error);
}

export { startInteractiveTest };
