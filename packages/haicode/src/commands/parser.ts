/**
 * Command parser for HaiCode Agent
 * Parses and validates command strings
 */

import type { ParsedCommand } from './types.js';

/**
 * Check if a message is a command (starts with /)
 */
export function isCommand(message: string): boolean {
  return message.trim().startsWith('/');
}

/**
 * Parse a command string into its components
 */
export function parseCommand(commandLine: string): ParsedCommand {
  const trimmed = commandLine.trim();
  
  if (!trimmed.startsWith('/')) {
    throw new Error('Invalid command: must start with /');
  }

  // Remove the leading slash and split by whitespace
  const parts = trimmed.substring(1).trim().split(/\s+/).filter(part => part.length > 0);
  
  if (parts.length === 0) {
    throw new Error('Invalid command: empty command');
  }

  const [command, subCommand, ...args] = parts;
  
  // Check if the second part is a sub-command or an argument
  // Sub-commands are only valid for certain commands (like stats)
  let finalSubCommand: string | undefined;
  let finalArgs: string[];

  if (command.toLowerCase() === 'stats' && subCommand) {
    // For stats command, check if it's a valid sub-command
    const validStatsSubCommands = ['model', 'models', 'tool', 'tools', 'session', 'all'];
    if (validStatsSubCommands.includes(subCommand.toLowerCase())) {
      finalSubCommand = subCommand.toLowerCase();
      finalArgs = args;
    } else {
      // Invalid sub-command for stats, keep it for validation error
      finalSubCommand = subCommand;
      finalArgs = args;
    }
  } else if ((command.toLowerCase() === 'sessions' || command.toLowerCase() === 'session' || command.toLowerCase() === 'sess') && subCommand) {
    // For sessions command, check if it's a valid sub-command
    const validSessionsSubCommands = ['list', 'ls', 'select', 'switch', 'use', 'details', 'detail', 'info', 'current', 'curr'];
    if (validSessionsSubCommands.includes(subCommand.toLowerCase())) {
      finalSubCommand = subCommand.toLowerCase();
      finalArgs = args;
    } else {
      // Invalid sub-command for sessions, keep it for validation error
      finalSubCommand = subCommand;
      finalArgs = args;
    }
  } else {
    // For other commands, treat everything as arguments
    finalSubCommand = undefined;
    finalArgs = subCommand ? [subCommand, ...args] : args;
  }

  return {
    command: command.toLowerCase(),
    subCommand: finalSubCommand,
    args: finalArgs,
    original: commandLine,
  };
}

/**
 * Validate a parsed command
 */
export function validateCommand(parsed: ParsedCommand): { valid: boolean; error?: string } {
  // Basic validation
  if (!parsed.command) {
    return { valid: false, error: 'Command name is required' };
  }

  // Command-specific validation
  switch (parsed.command) {
    case 'stats':
      return validateStatsCommand(parsed);
    case 'sessions':
    case 'session':
    case 'sess':
      return validateSessionsCommand(parsed);
    case 'help':
      return validateHelpCommand(parsed);
    case 'clear':
      return validateClearCommand(parsed);
    default:
      return { valid: false, error: `Unknown command: ${parsed.command}` };
  }
}

/**
 * Validate stats command
 */
function validateStatsCommand(parsed: ParsedCommand): { valid: boolean; error?: string } {
  const validSubCommands = ['model', 'models', 'tool', 'tools', 'session', 'all'];
  
  if (parsed.subCommand && !validSubCommands.includes(parsed.subCommand)) {
    return { 
      valid: false, 
      error: `Invalid stats sub-command: ${parsed.subCommand}. Valid options: ${validSubCommands.join(', ')}` 
    };
  }

  return { valid: true };
}

/**
 * Validate sessions command
 */
function validateSessionsCommand(parsed: ParsedCommand): { valid: boolean; error?: string } {
  const validSubCommands = ['list', 'ls', 'select', 'switch', 'use', 'details', 'detail', 'info', 'current', 'curr'];

  if (parsed.subCommand && !validSubCommands.includes(parsed.subCommand)) {
    return {
      valid: false,
      error: `Invalid sessions sub-command: ${parsed.subCommand}. Valid options: ${validSubCommands.join(', ')}`
    };
  }

  // Validate arguments for specific sub-commands
  if (parsed.subCommand === 'select' || parsed.subCommand === 'switch' || parsed.subCommand === 'use') {
    if (parsed.args.length === 0) {
      return {
        valid: false,
        error: `${parsed.subCommand} sub-command requires a session ID argument`
      };
    }
  }

  if (parsed.subCommand === 'details' || parsed.subCommand === 'detail' || parsed.subCommand === 'info') {
    if (parsed.args.length === 0) {
      return {
        valid: false,
        error: `${parsed.subCommand} sub-command requires a session ID argument`
      };
    }
  }

  return { valid: true };
}

/**
 * Validate help command
 */
function validateHelpCommand(parsed: ParsedCommand): { valid: boolean; error?: string } {
  // Help command can have optional arguments for specific command help
  return { valid: true };
}

/**
 * Validate clear command
 */
function validateClearCommand(parsed: ParsedCommand): { valid: boolean; error?: string } {
  // Clear command should not have sub-commands
  if (parsed.subCommand) {
    return { valid: false, error: 'Clear command does not accept sub-commands' };
  }
  
  return { valid: true };
}

/**
 * Format command help text
 */
export function formatCommandHelp(): string {
  return `
Available Commands:

/stats                   - Show session statistics
/stats model             - Show model-specific statistics
/stats models            - Show model-specific statistics (alias)
/stats tool              - Show tool usage statistics
/stats tools             - Show tool usage statistics (alias)
/stats session           - Show current session summary
/stats all               - Show all statistics

/sessions                - List recent conversation sessions
/sessions list [limit]   - List recent sessions (default: 10)
/sessions select <id>    - Switch to a specific session
/sessions details <id>   - Show detailed session information
/sessions current        - Show current session information

/help                    - Show this help message
/help <command>          - Show help for specific command

/clear                   - Clear token usage statistics

Examples:
  /stats                 - Basic session stats
  /stats model           - Detailed model breakdown
  /stats tools           - Tool usage analysis
  /sessions              - List recent sessions
  /sessions select abc123 - Switch to session starting with 'abc123'
  /help stats            - Help for stats command
`.trim();
}

/**
 * Format command-specific help
 */
export function formatCommandSpecificHelp(command: string): string {
  switch (command.toLowerCase()) {
    case 'stats':
      return `
Stats Command Help:

/stats                   - Show overall session statistics including:
                          • Total tokens used
                          • API calls made  
                          • Tool calls executed
                          • Cache efficiency
                          • Session duration

/stats model            - Show detailed model statistics:
                          • Per-model token usage
                          • API call counts
                          • Average latency
                          • Error rates

/stats tools            - Show tool usage statistics:
                          • Tool call counts
                          • Success rates
                          • Average execution time
                          • Failure analysis

/stats session          - Show compact session summary
/stats all              - Show comprehensive statistics

The stats command helps you monitor AI usage and optimize costs.
`.trim();

    case 'help':
      return `
Help Command:

/help                   - Show all available commands
/help <command>         - Show detailed help for a specific command

Examples:
  /help stats           - Show help for stats command
  /help clear           - Show help for clear command
`.trim();

    case 'clear':
      return `
Clear Command:

/clear                  - Clear all token usage statistics for the current session

This will reset:
• Token counters
• API call statistics  
• Tool usage data
• Performance metrics

Note: This only affects the current session. Historical data is preserved.
`.trim();

    case 'sessions':
    case 'session':
      return `
Sessions Command Help:

/sessions                - List recent conversation sessions (default: 10)
/sessions list [limit]   - List recent sessions with optional limit
/sessions select <id>    - Switch to a specific session by ID or prefix
/sessions details <id>   - Show detailed information about a session
/sessions current        - Show information about the current session

Session Management:
• Sessions are automatically saved and can be resumed
• Use partial session IDs for quick selection (e.g., 'abc123' for 'abc123def...')
• Session switching preserves conversation history
• MongoDB persistence must be enabled for session management

Examples:
  /sessions              - List recent sessions
  /sessions list 20      - List last 20 sessions
  /sessions select abc   - Switch to session starting with 'abc'
  /sessions details xyz  - Show details for session starting with 'xyz'
  /sessions current      - Show current session info

Aliases: /session, /sess
`.trim();

    default:
      return `No help available for command: ${command}`;
  }
}
