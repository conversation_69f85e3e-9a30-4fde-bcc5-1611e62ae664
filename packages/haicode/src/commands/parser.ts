/**
 * Command parser for HaiCode Agent
 * Parses and validates command strings
 */

import type { ParsedCommand } from './types.js';

/**
 * Check if a message is a command (starts with /)
 */
export function isCommand(message: string): boolean {
  return message.trim().startsWith('/');
}

/**
 * Parse a command string into its components
 */
export function parseCommand(commandLine: string): ParsedCommand {
  const trimmed = commandLine.trim();
  
  if (!trimmed.startsWith('/')) {
    throw new Error('Invalid command: must start with /');
  }

  // Remove the leading slash and split by whitespace
  const parts = trimmed.substring(1).trim().split(/\s+/).filter(part => part.length > 0);
  
  if (parts.length === 0) {
    throw new Error('Invalid command: empty command');
  }

  const [command, subCommand, ...args] = parts;
  
  // Check if the second part is a sub-command or an argument
  // Sub-commands are only valid for certain commands (like stats)
  let finalSubCommand: string | undefined;
  let finalArgs: string[];

  if (command.toLowerCase() === 'stats' && subCommand) {
    // For stats command, check if it's a valid sub-command
    const validStatsSubCommands = ['model', 'models', 'tool', 'tools', 'session', 'all'];
    if (validStatsSubCommands.includes(subCommand.toLowerCase())) {
      finalSubCommand = subCommand.toLowerCase();
      finalArgs = args;
    } else {
      // Invalid sub-command for stats, keep it for validation error
      finalSubCommand = subCommand;
      finalArgs = args;
    }
  } else {
    // For other commands, treat everything as arguments
    finalSubCommand = undefined;
    finalArgs = subCommand ? [subCommand, ...args] : args;
  }

  return {
    command: command.toLowerCase(),
    subCommand: finalSubCommand,
    args: finalArgs,
    original: commandLine,
  };
}

/**
 * Validate a parsed command
 */
export function validateCommand(parsed: ParsedCommand): { valid: boolean; error?: string } {
  // Basic validation
  if (!parsed.command) {
    return { valid: false, error: 'Command name is required' };
  }

  // Command-specific validation
  switch (parsed.command) {
    case 'stats':
      return validateStatsCommand(parsed);
    case 'help':
      return validateHelpCommand(parsed);
    case 'clear':
      return validateClearCommand(parsed);
    default:
      return { valid: false, error: `Unknown command: ${parsed.command}` };
  }
}

/**
 * Validate stats command
 */
function validateStatsCommand(parsed: ParsedCommand): { valid: boolean; error?: string } {
  const validSubCommands = ['model', 'models', 'tool', 'tools', 'session', 'all'];
  
  if (parsed.subCommand && !validSubCommands.includes(parsed.subCommand)) {
    return { 
      valid: false, 
      error: `Invalid stats sub-command: ${parsed.subCommand}. Valid options: ${validSubCommands.join(', ')}` 
    };
  }

  return { valid: true };
}

/**
 * Validate help command
 */
function validateHelpCommand(parsed: ParsedCommand): { valid: boolean; error?: string } {
  // Help command can have optional arguments for specific command help
  return { valid: true };
}

/**
 * Validate clear command
 */
function validateClearCommand(parsed: ParsedCommand): { valid: boolean; error?: string } {
  // Clear command should not have sub-commands
  if (parsed.subCommand) {
    return { valid: false, error: 'Clear command does not accept sub-commands' };
  }
  
  return { valid: true };
}

/**
 * Format command help text
 */
export function formatCommandHelp(): string {
  return `
Available Commands:

/stats                   - Show session statistics
/stats model             - Show model-specific statistics  
/stats models            - Show model-specific statistics (alias)
/stats tool              - Show tool usage statistics
/stats tools             - Show tool usage statistics (alias)
/stats session           - Show current session summary
/stats all               - Show all statistics

/help                    - Show this help message
/help <command>          - Show help for specific command

/clear                   - Clear token usage statistics

Examples:
  /stats                 - Basic session stats
  /stats model           - Detailed model breakdown
  /stats tools           - Tool usage analysis
  /help stats            - Help for stats command
`.trim();
}

/**
 * Format command-specific help
 */
export function formatCommandSpecificHelp(command: string): string {
  switch (command.toLowerCase()) {
    case 'stats':
      return `
Stats Command Help:

/stats                   - Show overall session statistics including:
                          • Total tokens used
                          • API calls made  
                          • Tool calls executed
                          • Cache efficiency
                          • Session duration

/stats model            - Show detailed model statistics:
                          • Per-model token usage
                          • API call counts
                          • Average latency
                          • Error rates

/stats tools            - Show tool usage statistics:
                          • Tool call counts
                          • Success rates
                          • Average execution time
                          • Failure analysis

/stats session          - Show compact session summary
/stats all              - Show comprehensive statistics

The stats command helps you monitor AI usage and optimize costs.
`.trim();

    case 'help':
      return `
Help Command:

/help                   - Show all available commands
/help <command>         - Show detailed help for a specific command

Examples:
  /help stats           - Show help for stats command
  /help clear           - Show help for clear command
`.trim();

    case 'clear':
      return `
Clear Command:

/clear                  - Clear all token usage statistics for the current session

This will reset:
• Token counters
• API call statistics  
• Tool usage data
• Performance metrics

Note: This only affects the current session. Historical data is preserved.
`.trim();

    default:
      return `No help available for command: ${command}`;
  }
}
