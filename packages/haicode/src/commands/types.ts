/**
 * Command system types for HaiCode Agent
 * Provides a simple command processing system similar to CLI slash commands
 */

import type { SessionTokenStats } from '../types/tokenUsage.js';

/**
 * Command execution context
 */
export interface CommandContext {
  /** Current session ID */
  sessionId: string;
  /** Agent instance for accessing statistics */
  agent: any; // Will be typed as Hai<PERSON>ode<PERSON>gent when imported
}

/**
 * Command execution result
 */
export interface CommandResult {
  /** Type of result */
  type: 'message' | 'stats' | 'error';
  /** Content to display to user */
  content: string;
  /** Optional structured data for stats */
  data?: {
    stats?: SessionTokenStats;
    aggregated?: any;
  };
}

/**
 * Command definition interface
 */
export interface Command {
  /** Command name (without the / prefix) */
  name: string;
  /** Alternative names for the command */
  aliases?: string[];
  /** Command description */
  description: string;
  /** Command execution function */
  execute: (context: CommandContext, args: string[]) => Promise<CommandResult> | CommandResult;
  /** Sub-commands */
  subCommands?: Command[];
}

/**
 * Command registry interface
 */
export interface CommandRegistry {
  /** Register a command */
  register(command: Command): void;
  /** Get a command by name */
  get(name: string): Command | undefined;
  /** Get all registered commands */
  getAll(): Command[];
  /** Execute a command */
  execute(commandLine: string, context: CommandContext): Promise<CommandResult>;
}

/**
 * Command parsing result
 */
export interface ParsedCommand {
  /** Command name */
  command: string;
  /** Sub-command name (if any) */
  subCommand?: string;
  /** Command arguments */
  args: string[];
  /** Original command line */
  original: string;
}

/**
 * Built-in command names
 */
export enum BuiltInCommands {
  STATS = 'stats',
  HELP = 'help',
  CLEAR = 'clear',
}

/**
 * Stats command sub-commands
 */
export enum StatsSubCommands {
  MODEL = 'model',
  MODELS = 'models',
  TOOL = 'tool',
  TOOLS = 'tools',
  SESSION = 'session',
  ALL = 'all',
}
