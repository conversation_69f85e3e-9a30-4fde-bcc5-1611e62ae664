/**
 * Command registry for HaiCode Agent
 * Manages command registration and execution
 */

import { logger } from '../utils/logger.js';
import { parseCommand, validateCommand, formatCommandHelp, formatCommandSpecificHelp } from './parser.js';
import { formatCLISessionStats, formatCLIModelStats, formatCLIToolStats, formatCLICompactSummary } from '../utils/cliFormatter.js';
import type { Command, CommandRegistry, CommandContext, CommandResult } from './types.js';
import type { SessionInfo } from '../index.js';

/**
 * Default command registry implementation
 */
export class DefaultCommandRegistry implements CommandRegistry {
  private commands = new Map<string, Command>();
  private aliases = new Map<string, string>(); // alias -> command name

  constructor() {
    // Register built-in commands
    this.registerBuiltInCommands();
  }

  /**
   * Register a command
   */
  register(command: Command): void {
    this.commands.set(command.name, command);
    
    // Register aliases
    if (command.aliases) {
      for (const alias of command.aliases) {
        this.aliases.set(alias, command.name);
      }
    }

    logger.debug(`[CommandRegistry] Registered command: ${command.name}`, {
      aliases: command.aliases,
      hasSubCommands: !!command.subCommands?.length,
    });
  }

  /**
   * Get a command by name or alias
   */
  get(name: string): Command | undefined {
    const commandName = this.aliases.get(name) || name;
    const command = this.commands.get(commandName);

    logger.debug(`[CommandRegistry] Looking for command: ${name}`, {
      resolvedName: commandName,
      found: !!command,
      availableCommands: Array.from(this.commands.keys()),
      availableAliases: Array.from(this.aliases.keys()),
    });

    return command;
  }

  /**
   * Get all registered commands
   */
  getAll(): Command[] {
    return Array.from(this.commands.values());
  }

  /**
   * Execute a command
   */
  async execute(commandLine: string, context: CommandContext): Promise<CommandResult> {
    try {
      // Parse the command
      const parsed = parseCommand(commandLine);

      logger.debug(`[CommandRegistry] Parsed command:`, {
        original: commandLine,
        parsed: parsed,
      });

      // Resolve alias to actual command name for validation
      const actualCommandName = this.aliases.get(parsed.command) || parsed.command;
      const validationParsed = { ...parsed, command: actualCommandName };

      // Validate the command
      const validation = validateCommand(validationParsed);
      if (!validation.valid) {
        logger.debug(`[CommandRegistry] Command validation failed:`, {
          command: parsed.command,
          error: validation.error,
        });
        return {
          type: 'error',
          content: `❌ ${validation.error}`,
        };
      }

      // Find the command
      const command = this.get(parsed.command);
      if (!command) {
        logger.debug(`[CommandRegistry] Command not found:`, {
          command: parsed.command,
          availableCommands: Array.from(this.commands.keys()),
        });
        return {
          type: 'error',
          content: `❌ Unknown command: ${parsed.command}\n\nType /help to see available commands.`,
        };
      }

      // Handle sub-commands
      if (parsed.subCommand && command.subCommands) {
        const subCommand = command.subCommands.find(
          sc => sc.name === parsed.subCommand || sc.aliases?.includes(parsed.subCommand!)
        );
        
        if (subCommand) {
          return await subCommand.execute(context, parsed.args);
        } else {
          return {
            type: 'error',
            content: `❌ Unknown sub-command: ${parsed.subCommand} for command ${parsed.command}`,
          };
        }
      }

      // Execute the main command
      return await command.execute(context, parsed.args);

    } catch (error) {
      logger.error('[CommandRegistry] Command execution failed:', { error: error instanceof Error ? error.message : String(error) });
      return {
        type: 'error',
        content: `❌ Command execution failed: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * Register built-in commands
   */
  private registerBuiltInCommands(): void {
    // Help command
    this.register({
      name: 'help',
      aliases: ['h', '?'],
      description: 'Show help information',
      execute: (context, args) => {
        if (args.length > 0) {
          const commandName = args[0];
          const helpText = formatCommandSpecificHelp(commandName);
          return {
            type: 'message',
            content: helpText,
          };
        } else {
          return {
            type: 'message',
            content: formatCommandHelp(),
          };
        }
      },
    });

    // Clear command
    this.register({
      name: 'clear',
      aliases: ['reset'],
      description: 'Clear token usage statistics',
      execute: (context, args) => {
        try {
          context.agent.clearTokenUsageStats(context.sessionId);
          return {
            type: 'message',
            content: '✅ Token usage statistics cleared for current session.',
          };
        } catch (error) {
          return {
            type: 'error',
            content: `❌ Failed to clear statistics: ${error instanceof Error ? error.message : String(error)}`,
          };
        }
      },
    });

    // Stats command with sub-commands
    this.register({
      name: 'stats',
      aliases: ['statistics', 'usage'],
      description: 'Show token usage statistics',
      execute: (context, args) => {
        // Default stats command shows general session stats
        try {
          const stats = context.agent.getTokenUsageStats(context.sessionId);
          if (!stats) {
            return {
              type: 'message',
              content: '📊 No statistics available for current session.\n\nStart a conversation to see token usage statistics.',
            };
          }

          const formattedStats = formatCLISessionStats(stats);
          return {
            type: 'stats',
            content: formattedStats,
            data: { stats },
          };
        } catch (error) {
          return {
            type: 'error',
            content: `❌ Failed to get statistics: ${error instanceof Error ? error.message : String(error)}`,
          };
        }
      },
      subCommands: [
        {
          name: 'model',
          aliases: ['models'],
          description: 'Show model-specific statistics',
          execute: (context, args) => {
            try {
              const stats = context.agent.getTokenUsageStats(context.sessionId);
              if (!stats || Object.keys(stats.models).length === 0) {
                return {
                  type: 'message',
                  content: '📊 No model statistics available.\n\nMake some API calls to see model usage statistics.',
                };
              }

              const formattedStats = formatCLIModelStats(stats.models);
              return {
                type: 'stats',
                content: formattedStats,
                data: { stats },
              };
            } catch (error) {
              return {
                type: 'error',
                content: `❌ Failed to get model statistics: ${error instanceof Error ? error.message : String(error)}`,
              };
            }
          },
        },
        {
          name: 'tools',
          aliases: ['tool'],
          description: 'Show tool usage statistics',
          execute: (context, args) => {
            try {
              const stats = context.agent.getTokenUsageStats(context.sessionId);
              if (!stats || Object.keys(stats.tools).length === 0) {
                return {
                  type: 'message',
                  content: '🔧 No tool statistics available.\n\nUse some tools to see usage statistics.',
                };
              }

              const formattedStats = formatCLIToolStats(stats.tools);
              return {
                type: 'stats',
                content: formattedStats,
                data: { stats },
              };
            } catch (error) {
              return {
                type: 'error',
                content: `❌ Failed to get tool statistics: ${error instanceof Error ? error.message : String(error)}`,
              };
            }
          },
        },
        {
          name: 'session',
          description: 'Show session summary',
          execute: (context, _args) => {
            try {
              const stats = context.agent.getTokenUsageStats(context.sessionId);
              if (!stats) {
                return {
                  type: 'message',
                  content: '📋 No session data available.',
                };
              }

              const summary = formatCLICompactSummary(stats);
              return {
                type: 'message',
                content: summary,
              };
            } catch (error) {
              return {
                type: 'error',
                content: `❌ Failed to get session summary: ${error instanceof Error ? error.message : String(error)}`,
              };
            }
          },
        },
        {
          name: 'all',
          description: 'Show comprehensive statistics',
          execute: (context, _args) => {
            try {
              const aggregated = context.agent.getFormattedAggregatedStats();
              return {
                type: 'stats',
                content: aggregated,
                data: {
                  aggregated: context.agent.getAggregatedTokenStats(),
                },
              };
            } catch (error) {
              return {
                type: 'error',
                content: `❌ Failed to get aggregated statistics: ${error instanceof Error ? error.message : String(error)}`,
              };
            }
          },
        },
      ],
    });

    // Sessions command with sub-commands
    this.register({
      name: 'sessions',
      aliases: ['session', 'sess'],
      description: 'Manage conversation sessions',
      execute: async (context, args) => {
        // Default sessions command shows recent sessions list
        try {
          const limit = args.length > 0 ? parseInt(args[0]) || 10 : 10;
          const sessions = await context.agent.getRecentSessions(limit);

          if (sessions.length === 0) {
            return {
              type: 'message',
              content: '📝 No sessions found.\n\nStart a conversation to create your first session.',
            };
          }

          let content = `📝 Recent Sessions (${sessions.length}):\n\n`;

          for (let i = 0; i < sessions.length; i++) {
            const session = sessions[i];
            const isCurrentSession = session.sessionId === context.sessionId;
            const indicator = isCurrentSession ? '👉 ' : '   ';
            const sessionIdShort = session.sessionId.substring(0, 8);
            const createdAt = session.createdAt.toLocaleString();
            const lastMessage = session.lastMessage || 'No messages';

            content += `${indicator}${i + 1}. ${sessionIdShort}... (${session.messageCount} msgs)\n`;
            content += `     Created: ${createdAt}\n`;
            content += `     Last: ${lastMessage}\n\n`;
          }

          content += 'Use `/sessions select <sessionId>` to switch to a session.\n';
          content += 'Use `/sessions details <sessionId>` for more information.';

          return {
            type: 'message',
            content,
          };
        } catch (error) {
          return {
            type: 'error',
            content: `❌ Failed to get sessions: ${error instanceof Error ? error.message : String(error)}`,
          };
        }
      },
      subCommands: [
        {
          name: 'list',
          aliases: ['ls'],
          description: 'List recent sessions',
          execute: async (context, args) => {
            try {
              const limit = args.length > 0 ? parseInt(args[0]) || 10 : 10;
              const sessions = await context.agent.getRecentSessions(limit);

              if (sessions.length === 0) {
                return {
                  type: 'message',
                  content: '📝 No sessions found.\n\nStart a conversation to create your first session.',
                };
              }

              let content = `📝 Recent Sessions (${sessions.length}):\n\n`;

              for (let i = 0; i < sessions.length; i++) {
                const session = sessions[i];
                const isCurrentSession = session.sessionId === context.sessionId;
                const indicator = isCurrentSession ? '👉 ' : '   ';
                const sessionIdShort = session.sessionId.substring(0, 8);
                const createdAt = session.createdAt.toLocaleString();
                const lastMessage = session.lastMessage || 'No messages';

                content += `${indicator}${i + 1}. ${sessionIdShort}... (${session.messageCount} msgs)\n`;
                content += `     Created: ${createdAt}\n`;
                content += `     Last: ${lastMessage}\n\n`;
              }

              content += 'Use `/sessions select <sessionId>` to switch to a session.\n';
              content += 'Use `/sessions details <sessionId>` for more information.';

              return {
                type: 'message',
                content,
              };
            } catch (error) {
              return {
                type: 'error',
                content: `❌ Failed to list sessions: ${error instanceof Error ? error.message : String(error)}`,
              };
            }
          },
        },
        {
          name: 'select',
          aliases: ['switch', 'use'],
          description: 'Select and switch to a session',
          execute: async (context, args) => {
            if (args.length === 0) {
              return {
                type: 'error',
                content: '❌ Please provide a session ID.\n\nUsage: /sessions select <sessionId>',
              };
            }

            try {
              const targetSessionId = args[0];
              const sessions = await context.agent.getRecentSessions(50); // Get more sessions for matching

              // Find session by exact match or prefix match
              let matchedSession = sessions.find((s: SessionInfo) => s.sessionId === targetSessionId);
              if (!matchedSession) {
                matchedSession = sessions.find((s: SessionInfo) => s.sessionId.startsWith(targetSessionId));
              }

              if (!matchedSession) {
                return {
                  type: 'error',
                  content: `❌ Session not found: ${targetSessionId}\n\nUse \`/sessions list\` to see available sessions.`,
                };
              }

              // Update the agent's current session ID
              context.agent.setCurrentSessionId(matchedSession.sessionId);

              return {
                type: 'message',
                content: `✅ Switching to session: ${matchedSession.sessionId.substring(0, 8)}...\n` +
                        `Created: ${matchedSession.createdAt.toLocaleString()}\n` +
                        `Messages: ${matchedSession.messageCount}\n\n` +
                        `🔄 Session switched! Continue your conversation...`,
                data: {
                  sessionSwitch: {
                    newSessionId: matchedSession.sessionId,
                    sessionInfo: matchedSession,
                  },
                },
              };
            } catch (error) {
              return {
                type: 'error',
                content: `❌ Failed to select session: ${error instanceof Error ? error.message : String(error)}`,
              };
            }
          },
        },
        {
          name: 'details',
          aliases: ['detail', 'info'],
          description: 'Show detailed information about a session',
          execute: async (context, args) => {
            if (args.length === 0) {
              return {
                type: 'error',
                content: '❌ Please provide a session ID.\n\nUsage: /sessions details <sessionId>',
              };
            }

            try {
              const targetSessionId = args[0];
              const sessions = await context.agent.getRecentSessions(50);

              // Find session by exact match or prefix match
              let matchedSession = sessions.find((s: SessionInfo) => s.sessionId === targetSessionId);
              if (!matchedSession) {
                matchedSession = sessions.find((s: SessionInfo) => s.sessionId.startsWith(targetSessionId));
              }

              if (!matchedSession) {
                return {
                  type: 'error',
                  content: `❌ Session not found: ${targetSessionId}\n\nUse \`/sessions list\` to see available sessions.`,
                };
              }

              // Get detailed session information
              const details = await context.agent.getSessionDetails(matchedSession.sessionId);
              if (!details) {
                return {
                  type: 'error',
                  content: `❌ Could not retrieve details for session: ${matchedSession.sessionId}`,
                };
              }

              let content = `📋 Session Details:\n\n`;
              content += `🆔 ID: ${details.sessionId}\n`;
              content += `📅 Created: ${details.createdAt.toLocaleString()}\n`;
              content += `🕒 Updated: ${details.updatedAt.toLocaleString()}\n`;
              content += `💬 Messages: ${details.messageCount}\n\n`;

              if (details.messages.length > 0) {
                content += `📝 Recent Messages:\n`;
                const recentMessages = details.messages.slice(-3); // Show last 3 messages

                for (const msg of recentMessages) {
                  const msgType = msg.constructor?.name || 'Message';
                  const msgContent = typeof msg.content === 'string'
                    ? msg.content.substring(0, 100) + (msg.content.length > 100 ? '...' : '')
                    : JSON.stringify(msg.content).substring(0, 100) + '...';

                  content += `  • ${msgType}: ${msgContent}\n`;
                }

                if (details.messages.length > 3) {
                  content += `  ... and ${details.messages.length - 3} more messages\n`;
                }
              }

              return {
                type: 'message',
                content,
              };
            } catch (error) {
              return {
                type: 'error',
                content: `❌ Failed to get session details: ${error instanceof Error ? error.message : String(error)}`,
              };
            }
          },
        },
        {
          name: 'current',
          aliases: ['curr'],
          description: 'Show current session information',
          execute: async (context, _args) => {
            try {
              const sessions = await context.agent.getRecentSessions(50);
              const currentSession = sessions.find((s: SessionInfo) => s.sessionId === context.sessionId);

              if (!currentSession) {
                return {
                  type: 'message',
                  content: `📋 Current Session: ${context.sessionId}\n\n⚠️  This appears to be a new session with no stored history.`,
                };
              }

              let content = `📋 Current Session:\n\n`;
              content += `🆔 ID: ${currentSession.sessionId}\n`;
              content += `📅 Created: ${currentSession.createdAt.toLocaleString()}\n`;
              content += `🕒 Updated: ${currentSession.updatedAt.toLocaleString()}\n`;
              content += `💬 Messages: ${currentSession.messageCount}\n`;

              if (currentSession.lastMessage) {
                content += `💭 Last: ${currentSession.lastMessage}\n`;
              }

              return {
                type: 'message',
                content,
              };
            } catch (error) {
              return {
                type: 'error',
                content: `❌ Failed to get current session info: ${error instanceof Error ? error.message : String(error)}`,
              };
            }
          },
        },
      ],
    });
  }
}
