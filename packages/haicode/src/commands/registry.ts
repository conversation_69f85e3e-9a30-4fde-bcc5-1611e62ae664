/**
 * Command registry for HaiCode Agent
 * Manages command registration and execution
 */

import { logger } from '../utils/logger.js';
import { parseCommand, validateCommand, formatCommandHelp, formatCommandSpecificHelp } from './parser.js';
import { formatCLISessionStats, formatCLIModelStats, formatCLIToolStats, formatCLICompactSummary } from '../utils/cliFormatter.js';
import type { Command, CommandRegistry, CommandContext, CommandResult } from './types.js';

/**
 * Default command registry implementation
 */
export class DefaultCommandRegistry implements CommandRegistry {
  private commands = new Map<string, Command>();
  private aliases = new Map<string, string>(); // alias -> command name

  constructor() {
    // Register built-in commands
    this.registerBuiltInCommands();
  }

  /**
   * Register a command
   */
  register(command: Command): void {
    this.commands.set(command.name, command);
    
    // Register aliases
    if (command.aliases) {
      for (const alias of command.aliases) {
        this.aliases.set(alias, command.name);
      }
    }

    logger.debug(`[CommandRegistry] Registered command: ${command.name}`, {
      aliases: command.aliases,
      hasSubCommands: !!command.subCommands?.length,
    });
  }

  /**
   * Get a command by name or alias
   */
  get(name: string): Command | undefined {
    const commandName = this.aliases.get(name) || name;
    return this.commands.get(commandName);
  }

  /**
   * Get all registered commands
   */
  getAll(): Command[] {
    return Array.from(this.commands.values());
  }

  /**
   * Execute a command
   */
  async execute(commandLine: string, context: CommandContext): Promise<CommandResult> {
    try {
      // Parse the command
      const parsed = parseCommand(commandLine);

      // Resolve alias to actual command name for validation
      const actualCommandName = this.aliases.get(parsed.command) || parsed.command;
      const validationParsed = { ...parsed, command: actualCommandName };

      // Validate the command
      const validation = validateCommand(validationParsed);
      if (!validation.valid) {
        return {
          type: 'error',
          content: `❌ ${validation.error}`,
        };
      }

      // Find the command
      const command = this.get(parsed.command);
      if (!command) {
        return {
          type: 'error',
          content: `❌ Unknown command: ${parsed.command}\n\nType /help to see available commands.`,
        };
      }

      // Handle sub-commands
      if (parsed.subCommand && command.subCommands) {
        const subCommand = command.subCommands.find(
          sc => sc.name === parsed.subCommand || sc.aliases?.includes(parsed.subCommand!)
        );
        
        if (subCommand) {
          return await subCommand.execute(context, parsed.args);
        } else {
          return {
            type: 'error',
            content: `❌ Unknown sub-command: ${parsed.subCommand} for command ${parsed.command}`,
          };
        }
      }

      // Execute the main command
      return await command.execute(context, parsed.args);

    } catch (error) {
      logger.error('[CommandRegistry] Command execution failed:', { error: error instanceof Error ? error.message : String(error) });
      return {
        type: 'error',
        content: `❌ Command execution failed: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * Register built-in commands
   */
  private registerBuiltInCommands(): void {
    // Help command
    this.register({
      name: 'help',
      aliases: ['h', '?'],
      description: 'Show help information',
      execute: (context, args) => {
        if (args.length > 0) {
          const commandName = args[0];
          const helpText = formatCommandSpecificHelp(commandName);
          return {
            type: 'message',
            content: helpText,
          };
        } else {
          return {
            type: 'message',
            content: formatCommandHelp(),
          };
        }
      },
    });

    // Clear command
    this.register({
      name: 'clear',
      aliases: ['reset'],
      description: 'Clear token usage statistics',
      execute: (context, args) => {
        try {
          context.agent.clearTokenUsageStats(context.sessionId);
          return {
            type: 'message',
            content: '✅ Token usage statistics cleared for current session.',
          };
        } catch (error) {
          return {
            type: 'error',
            content: `❌ Failed to clear statistics: ${error instanceof Error ? error.message : String(error)}`,
          };
        }
      },
    });

    // Stats command with sub-commands
    this.register({
      name: 'stats',
      aliases: ['statistics', 'usage'],
      description: 'Show token usage statistics',
      execute: (context, args) => {
        // Default stats command shows general session stats
        try {
          const stats = context.agent.getTokenUsageStats(context.sessionId);
          if (!stats) {
            return {
              type: 'message',
              content: '📊 No statistics available for current session.\n\nStart a conversation to see token usage statistics.',
            };
          }

          const formattedStats = formatCLISessionStats(stats);
          return {
            type: 'stats',
            content: formattedStats,
            data: { stats },
          };
        } catch (error) {
          return {
            type: 'error',
            content: `❌ Failed to get statistics: ${error instanceof Error ? error.message : String(error)}`,
          };
        }
      },
      subCommands: [
        {
          name: 'model',
          aliases: ['models'],
          description: 'Show model-specific statistics',
          execute: (context, args) => {
            try {
              const stats = context.agent.getTokenUsageStats(context.sessionId);
              if (!stats || Object.keys(stats.models).length === 0) {
                return {
                  type: 'message',
                  content: '📊 No model statistics available.\n\nMake some API calls to see model usage statistics.',
                };
              }

              const formattedStats = formatCLIModelStats(stats.models);
              return {
                type: 'stats',
                content: formattedStats,
                data: { stats },
              };
            } catch (error) {
              return {
                type: 'error',
                content: `❌ Failed to get model statistics: ${error instanceof Error ? error.message : String(error)}`,
              };
            }
          },
        },
        {
          name: 'tools',
          aliases: ['tool'],
          description: 'Show tool usage statistics',
          execute: (context, args) => {
            try {
              const stats = context.agent.getTokenUsageStats(context.sessionId);
              if (!stats || Object.keys(stats.tools).length === 0) {
                return {
                  type: 'message',
                  content: '🔧 No tool statistics available.\n\nUse some tools to see usage statistics.',
                };
              }

              const formattedStats = formatCLIToolStats(stats.tools);
              return {
                type: 'stats',
                content: formattedStats,
                data: { stats },
              };
            } catch (error) {
              return {
                type: 'error',
                content: `❌ Failed to get tool statistics: ${error instanceof Error ? error.message : String(error)}`,
              };
            }
          },
        },
        {
          name: 'session',
          description: 'Show session summary',
          execute: (context, _args) => {
            try {
              const stats = context.agent.getTokenUsageStats(context.sessionId);
              if (!stats) {
                return {
                  type: 'message',
                  content: '📋 No session data available.',
                };
              }

              const summary = formatCLICompactSummary(stats);
              return {
                type: 'message',
                content: summary,
              };
            } catch (error) {
              return {
                type: 'error',
                content: `❌ Failed to get session summary: ${error instanceof Error ? error.message : String(error)}`,
              };
            }
          },
        },
        {
          name: 'all',
          description: 'Show comprehensive statistics',
          execute: (context, _args) => {
            try {
              const aggregated = context.agent.getFormattedAggregatedStats();
              return {
                type: 'stats',
                content: aggregated,
                data: {
                  aggregated: context.agent.getAggregatedTokenStats(),
                },
              };
            } catch (error) {
              return {
                type: 'error',
                content: `❌ Failed to get aggregated statistics: ${error instanceof Error ? error.message : String(error)}`,
              };
            }
          },
        },
      ],
    });
  }
}
