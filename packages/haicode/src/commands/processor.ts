/**
 * Command processor for HaiCode Agent
 * Handles command detection and execution
 */

import { logger } from '../utils/logger.js';
import { isCommand } from './parser.js';
import { DefaultCommandRegistry } from './registry.js';
import type { CommandRegistry, CommandContext, CommandResult } from './types.js';

/**
 * Command processor class
 */
export class CommandProcessor {
  private registry: CommandRegistry;

  constructor(registry?: CommandRegistry) {
    this.registry = registry || new DefaultCommandRegistry();
  }

  /**
   * Check if a message is a command
   */
  isCommand(message: string): boolean {
    return isCommand(message);
  }

  /**
   * Process a command message
   */
  async processCommand(
    message: string,
    context: CommandContext
  ): Promise<CommandResult> {
    if (!this.isCommand(message)) {
      throw new Error('Message is not a command');
    }

    logger.debug('[CommandProcessor] Processing command:', { 
      command: message,
      sessionId: context.sessionId,
    });

    try {
      const result = await this.registry.execute(message, context);
      
      logger.debug('[CommandProcessor] Command executed:', {
        type: result.type,
        hasData: !!result.data,
      });

      return result;
    } catch (error) {
      logger.error('[CommandProcessor] Command processing failed:', { 
        error: error instanceof Error ? error.message : String(error),
      });

      return {
        type: 'error',
        content: `❌ Command processing failed: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * Get the command registry
   */
  getRegistry(): CommandRegistry {
    return this.registry;
  }

  /**
   * Register a custom command
   */
  registerCommand(command: import('./types.js').Command): void {
    this.registry.register(command);
  }
}

/**
 * Default command processor instance
 */
export const defaultCommandProcessor = new CommandProcessor();
