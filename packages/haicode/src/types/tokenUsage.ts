/**
 * Token usage statistics types for HaiCode Agent
 * Based on LangChain.js usage_metadata and packages/core implementation
 */

/**
 * Basic token usage information from a single LLM call
 * Compatible with LangChain.js AIMessage.usage_metadata
 */
export interface TokenUsage {
  /** Number of input/prompt tokens */
  input_tokens: number;
  /** Number of output/completion tokens */
  output_tokens: number;
  /** Total tokens (input + output) */
  total_tokens: number;
  /** Number of cached tokens (if supported by provider) */
  cached_tokens?: number;
  /** Number of tokens used for reasoning/thinking (if supported) */
  reasoning_tokens?: number;
  /** Number of tokens used for tool calls */
  tool_tokens?: number;
  /** Additional provider-specific token details */
  input_token_details?: {
    audio?: number;
    cache_read?: number;
    cache_creation?: number;
  };
  output_token_details?: {
    audio?: number;
    reasoning?: number;
  };
}

/**
 * Token usage aggregated by model
 */
export interface ModelTokenUsage extends TokenUsage {
  /** Model name/identifier */
  model: string;
  /** Number of API calls made to this model */
  call_count: number;
  /** Total latency for all calls to this model (in milliseconds) */
  total_latency_ms: number;
  /** Average latency per call (in milliseconds) */
  average_latency_ms: number;
}

/**
 * Tool call statistics
 */
export interface ToolCallStats {
  /** Tool name */
  name: string;
  /** Number of times this tool was called */
  call_count: number;
  /** Number of successful calls */
  success_count: number;
  /** Number of failed calls */
  failure_count: number;
  /** Total execution time for all calls (in milliseconds) */
  total_duration_ms: number;
  /** Average execution time per call (in milliseconds) */
  average_duration_ms: number;
  /** Success rate as percentage */
  success_rate: number;
}

/**
 * Session-level token usage statistics
 */
export interface SessionTokenStats {
  /** Session identifier */
  session_id: string;
  /** Session start time */
  start_time: Date;
  /** Session end time (if session is completed) */
  end_time?: Date;
  /** Total session duration in milliseconds */
  duration_ms?: number;
  
  /** Token usage aggregated by model */
  models: Record<string, ModelTokenUsage>;
  
  /** Tool call statistics */
  tools: Record<string, ToolCallStats>;
  
  /** Overall session totals */
  totals: {
    /** Total tokens across all models */
    total_tokens: number;
    /** Total input tokens across all models */
    total_input_tokens: number;
    /** Total output tokens across all models */
    total_output_tokens: number;
    /** Total cached tokens across all models */
    total_cached_tokens: number;
    /** Total API calls across all models */
    total_api_calls: number;
    /** Total tool calls across all tools */
    total_tool_calls: number;
    /** Total successful tool calls */
    total_successful_tool_calls: number;
    /** Total API latency across all calls */
    total_api_latency_ms: number;
    /** Total tool execution time */
    total_tool_duration_ms: number;
    /** Cache efficiency as percentage */
    cache_efficiency: number;
    /** Tool success rate as percentage */
    tool_success_rate: number;
  };
}

/**
 * Real-time token usage event
 */
export interface TokenUsageEvent {
  /** Event type */
  type: 'llm_call' | 'tool_call' | 'session_start' | 'session_end';
  /** Timestamp when the event occurred */
  timestamp: Date;
  /** Session identifier */
  session_id: string;
  /** Model name (for llm_call events) */
  model?: string;
  /** Tool name (for tool_call events) */
  tool_name?: string;
  /** Token usage for this specific event */
  token_usage?: TokenUsage;
  /** Call duration in milliseconds */
  duration_ms?: number;
  /** Whether the call was successful */
  success?: boolean;
  /** Error message if call failed */
  error?: string;
}

/**
 * Token usage configuration options
 */
export interface TokenUsageConfig {
  /** Whether to enable token usage tracking */
  enabled: boolean;
  /** Whether to track detailed token breakdowns */
  track_details: boolean;
  /** Whether to track tool call statistics */
  track_tools: boolean;
  /** Whether to track per-model statistics */
  track_per_model: boolean;
  /** Maximum number of sessions to keep in memory */
  max_sessions: number;
  /** Whether to persist statistics to storage */
  persist_stats: boolean;
}

/**
 * Token usage callback interface
 */
export interface TokenUsageCallback {
  /** Called when a new token usage event occurs */
  onTokenUsage?: (event: TokenUsageEvent) => void;
  /** Called when session statistics are updated */
  onSessionUpdate?: (stats: SessionTokenStats) => void;
  /** Called when an error occurs during tracking */
  onError?: (error: Error) => void;
}
