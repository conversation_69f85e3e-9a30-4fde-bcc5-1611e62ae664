#!/usr/bin/env node

/**
 * Hai Code CLI
 * A command-line interface for the Hai Code Agent with streaming support
 */

import chalk from 'chalk';
import { HaiCodeAgent } from './index.js';
import { parseCliArguments } from './utils/parseArgs.js';
import { readStdin } from './utils/readStdin.js';
import { runInteractiveMode } from './utils/interactive.js';
import { getLangfuseHelper } from './utils/langfuseHelper.js';
import { getVersion } from './utils/version.js';
import { getUserInfo } from './utils/user.js';
import { getUUID } from './utils/uuid.js';
import { logger, enableDebugMode } from './utils/logger.js';

function printHelp() {
  console.log(chalk.cyan(`
Hai Code Agent - AI Coding Assistant CLI

Usage:
  hai-code [options] [prompt]
  echo "your question" | hai-code

Options:
  -m, --model <model>     Model to use (default: ht::saas-deepseek-v3)
  -p, --prompt <prompt>   Prompt to process (non-interactive mode)
  -b, --base-url <url>    Base URL for LLM API
  -i, --interactive       Start in interactive mode (default if no prompt)
  -s, --session-id <id>   Session ID for conversation continuity
  -d, --debug             Enable debug mode
  -h, --help              Show this help message
  -v, --version           Show version

Environment Variables:
  HAI_CODE_MODEL          Default model to use

  Langfuse (可观测性):
  LANGFUSE_ENABLED        启用/禁用 Langfuse (true/false)

Examples:
  hai-code "What is the capital of France?"
  hai-code -m ht::saas-deepseek-v3 "Explain quantum computing"
  hai-code -s my-session "Hello" && hai-code -s my-session "Remember me?"
  echo "Fix this code" | hai-code
  OPENAI_BASE_URL=http://localhost:11434/v1 hai-code -m llama2 "Hello"
`));
}

function printVersion() {
  const version = getVersion();
  console.log(`hai-code version ${version}`);
}

async function main() {
  try {
    const { options, prompt } = await parseCliArguments();

    // Handle EPIPE errors when piping output to commands that close early (e.g., head)
    process.stdout.on('error', (err: NodeJS.ErrnoException) => {
      if (err && err.code === 'EPIPE') {
        process.exit(0);
      }
    });

    if (options.help) {
      printHelp();
      process.exit(0);
    }

    if (options.version) {
      printVersion();
      process.exit(0);
    }

    // 获取 Langfuse 状态信息
    const langfuseHelper = getLangfuseHelper();
    const langfuseConfig = langfuseHelper.getConfig();

    // 生成或使用提供的 sessionId
    const sessionId = options.sessionId || getUUID();
    const userInfo = getUserInfo();

    if (options.debug) {
      enableDebugMode();
      logger.debug('调试模式已启用');
      logger.debug('配置信息', {
        model: options.model,
        baseUrl: options.baseUrl,
        sessionId,
        langfuse: langfuseConfig.enabled ? 'enabled' : 'disabled',
        langfuseUrl: langfuseConfig.enabled ? langfuseConfig.baseUrl : undefined,
      });
    }

    // Create the agent instance with session management
    const agent = new HaiCodeAgent({
      model: options.model,
      baseUrl: options.baseUrl,
      apiKey: process.env.OPENAI_API_KEY,
      enablePersistence: true, // 启用会话持久化
      enableMongoPersistence: true, // 启用 MongoDB 持久化以支持 /sessions 命令
      langfuse: {
        enabled: langfuseConfig.enabled,
        userId: userInfo.userName,
        sessionId, // 使用相同的 sessionId 确保 Langfuse 和会话记录一致
        version: getVersion(),
      },
      // mcp: {
      //   enabled: true,
      //   servers: {
      //     "time-server": {
      //       command: "uvx",
      //       args: ["mcp-server-time"],
      //       transport: "stdio",
      //     }
      //   },
      // },
    });

    // Determine mode based on explicit flags and input availability
    if (options.interactive) {
      // Explicitly requested interactive mode
      await runInteractiveMode(agent, sessionId);
    } else if (prompt) {
      // Explicit prompt provided via command line
      try {
        for await (const chunk of agent.streamMessage(prompt, sessionId)) {
          process.stdout.write(chunk);
        }
        console.log(''); // Add final newline
        await agent.flushLangfuse();
        process.exit(0);
      } catch (error) {
        logger.error('处理消息时出错', { error: error instanceof Error ? error.message : String(error) });
        await agent.flushLangfuse();
        process.exit(1);
      }
    } else if (!process.stdin.isTTY) {
      // Input from stdin (pipe)
      const stdinInput = await readStdin();
      if (stdinInput.trim()) {
        try {
          for await (const chunk of agent.streamMessage(stdinInput.trim(), sessionId)) {
            process.stdout.write(chunk);
          }
          console.log(''); // Add final newline
          await agent.flushLangfuse();
          process.exit(0);
        } catch (error) {
          logger.error('处理 stdin 输入时出错', { error: error instanceof Error ? error.message : String(error) });
          await agent.flushLangfuse();
          process.exit(1);
        }
      } else {
        // Empty stdin, default to interactive
        await runInteractiveMode(agent, sessionId);
      }
    } else {
      // No explicit prompt and TTY available, default to interactive
      await runInteractiveMode(agent, sessionId);
    }
  } catch (error) {
    logger.error('Fatal error:', { 
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });
    process.exit(1);
  }
}

// Run the CLI
main().catch((error) => {
  logger.error('Unhandled error:', { error: error instanceof Error ? error.message : String(error) });
  process.exit(1);
});
