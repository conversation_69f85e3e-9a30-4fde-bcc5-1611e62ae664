/**
 * Statistics formatting utilities for HaiCode Agent
 * Provides human-readable formatting of token usage statistics
 */

import type { SessionTokenStats, ModelTokenUsage, ToolCallStats } from '../types/tokenUsage.js';

/**
 * Format duration in milliseconds to human-readable string
 */
export function formatDuration(durationMs: number): string {
  if (durationMs < 1000) {
    return `${durationMs}ms`;
  }
  
  const seconds = Math.floor(durationMs / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  
  if (hours > 0) {
    return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else {
    return `${seconds}s`;
  }
}

/**
 * Format number with thousands separators
 */
export function formatNumber(num: number): string {
  return num.toLocaleString();
}

/**
 * Format percentage with specified decimal places
 */
export function formatPercentage(value: number, decimals: number = 1): string {
  return `${value.toFixed(decimals)}%`;
}

/**
 * Format bytes to human-readable string
 */
export function formatBytes(bytes: number): string {
  const sizes = ['B', 'KB', 'MB', 'GB'];
  if (bytes === 0) return '0 B';
  
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
}

/**
 * Format session statistics to human-readable string
 */
export function formatSessionStats(stats: SessionTokenStats): string {
  const lines: string[] = [];
  
  // Session header
  lines.push(`📊 Session Statistics: ${stats.session_id}`);
  lines.push(`⏰ Started: ${stats.start_time.toLocaleString()}`);
  
  if (stats.end_time) {
    lines.push(`🏁 Ended: ${stats.end_time.toLocaleString()}`);
  }
  
  if (stats.duration_ms) {
    lines.push(`⏱️  Duration: ${formatDuration(stats.duration_ms)}`);
  }
  
  lines.push('');
  
  // Overall totals
  lines.push('🔢 Overall Totals:');
  lines.push(`  • Total Tokens: ${formatNumber(stats.totals.total_tokens)}`);
  lines.push(`  • Input Tokens: ${formatNumber(stats.totals.total_input_tokens)}`);
  lines.push(`  • Output Tokens: ${formatNumber(stats.totals.total_output_tokens)}`);
  
  if (stats.totals.total_cached_tokens > 0) {
    lines.push(`  • Cached Tokens: ${formatNumber(stats.totals.total_cached_tokens)}`);
    lines.push(`  • Cache Efficiency: ${formatPercentage(stats.totals.cache_efficiency)}`);
  }
  
  lines.push(`  • API Calls: ${formatNumber(stats.totals.total_api_calls)}`);
  
  if (stats.totals.total_api_latency_ms > 0) {
    lines.push(`  • Total API Time: ${formatDuration(stats.totals.total_api_latency_ms)}`);
    const avgLatency = stats.totals.total_api_calls > 0 ? 
      stats.totals.total_api_latency_ms / stats.totals.total_api_calls : 0;
    lines.push(`  • Average API Time: ${formatDuration(avgLatency)}`);
  }
  
  lines.push('');
  
  // Model breakdown
  const modelEntries = Object.entries(stats.models);
  if (modelEntries.length > 0) {
    lines.push('🤖 Model Breakdown:');
    for (const [modelName, modelStats] of modelEntries) {
      lines.push(`  ${modelName}:`);
      lines.push(`    • Calls: ${formatNumber(modelStats.call_count)}`);
      lines.push(`    • Tokens: ${formatNumber(modelStats.total_tokens)}`);
      lines.push(`    • Input: ${formatNumber(modelStats.input_tokens)}`);
      lines.push(`    • Output: ${formatNumber(modelStats.output_tokens)}`);
      
      if (modelStats.cached_tokens && modelStats.cached_tokens > 0) {
        lines.push(`    • Cached: ${formatNumber(modelStats.cached_tokens)}`);
      }
      
      if (modelStats.average_latency_ms > 0) {
        lines.push(`    • Avg Latency: ${formatDuration(modelStats.average_latency_ms)}`);
      }
    }
    lines.push('');
  }
  
  // Tool breakdown
  const toolEntries = Object.entries(stats.tools);
  if (toolEntries.length > 0) {
    lines.push('🔧 Tool Usage:');
    lines.push(`  • Total Calls: ${formatNumber(stats.totals.total_tool_calls)}`);
    lines.push(`  • Successful: ${formatNumber(stats.totals.total_successful_tool_calls)}`);
    lines.push(`  • Success Rate: ${formatPercentage(stats.totals.tool_success_rate)}`);
    
    if (stats.totals.total_tool_duration_ms > 0) {
      lines.push(`  • Total Time: ${formatDuration(stats.totals.total_tool_duration_ms)}`);
    }
    
    lines.push('');
    lines.push('  Tool Breakdown:');
    
    // Sort tools by call count
    const sortedTools = toolEntries.sort(([, a], [, b]) => b.call_count - a.call_count);
    
    for (const [toolName, toolStats] of sortedTools) {
      lines.push(`    ${toolName}:`);
      lines.push(`      • Calls: ${formatNumber(toolStats.call_count)}`);
      lines.push(`      • Success: ${formatNumber(toolStats.success_count)}`);
      lines.push(`      • Failed: ${formatNumber(toolStats.failure_count)}`);
      lines.push(`      • Success Rate: ${formatPercentage(toolStats.success_rate)}`);
      
      if (toolStats.average_duration_ms > 0) {
        lines.push(`      • Avg Duration: ${formatDuration(toolStats.average_duration_ms)}`);
      }
    }
  }
  
  return lines.join('\n');
}

/**
 * Format aggregated statistics to human-readable string
 */
export function formatAggregatedStats(stats: ReturnType<import('./tokenUsageTracker.js').TokenUsageTracker['getAggregatedStats']>): string {
  const lines: string[] = [];
  
  lines.push('📈 Aggregated Statistics (All Sessions)');
  lines.push('');
  
  // Session overview
  lines.push('📊 Session Overview:');
  lines.push(`  • Total Sessions: ${formatNumber(stats.total_sessions)}`);
  lines.push(`  • Active Sessions: ${formatNumber(stats.active_sessions)}`);
  lines.push('');
  
  // Overall totals
  lines.push('🔢 Overall Totals:');
  lines.push(`  • Total Tokens: ${formatNumber(stats.total_tokens)}`);
  lines.push(`  • Total API Calls: ${formatNumber(stats.total_api_calls)}`);
  lines.push(`  • Total Tool Calls: ${formatNumber(stats.total_tool_calls)}`);
  lines.push('');
  
  // Model breakdown
  const modelEntries = Object.entries(stats.models);
  if (modelEntries.length > 0) {
    lines.push('🤖 Model Usage:');
    
    // Sort models by total tokens
    const sortedModels = modelEntries.sort(([, a], [, b]) => b.total_tokens - a.total_tokens);
    
    for (const [modelName, modelStats] of sortedModels) {
      lines.push(`  ${modelName}:`);
      lines.push(`    • Calls: ${formatNumber(modelStats.call_count)}`);
      lines.push(`    • Tokens: ${formatNumber(modelStats.total_tokens)}`);
      
      if (modelStats.average_latency_ms > 0) {
        lines.push(`    • Avg Latency: ${formatDuration(modelStats.average_latency_ms)}`);
      }
    }
    lines.push('');
  }
  
  // Tool breakdown
  const toolEntries = Object.entries(stats.tools);
  if (toolEntries.length > 0) {
    lines.push('🔧 Tool Usage:');
    
    // Sort tools by call count
    const sortedTools = toolEntries.sort(([, a], [, b]) => b.call_count - a.call_count);
    
    for (const [toolName, toolStats] of sortedTools) {
      lines.push(`  ${toolName}:`);
      lines.push(`    • Calls: ${formatNumber(toolStats.call_count)}`);
      lines.push(`    • Success Rate: ${formatPercentage(toolStats.success_rate)}`);
      
      if (toolStats.average_duration_ms > 0) {
        lines.push(`    • Avg Duration: ${formatDuration(toolStats.average_duration_ms)}`);
      }
    }
  }
  
  return lines.join('\n');
}

/**
 * Format compact session summary
 */
export function formatCompactSessionSummary(stats: SessionTokenStats): string {
  const duration = stats.duration_ms ? formatDuration(stats.duration_ms) : 'ongoing';
  const cacheInfo = stats.totals.total_cached_tokens > 0 ? 
    ` (${formatPercentage(stats.totals.cache_efficiency)} cached)` : '';
  
  return `Session ${stats.session_id}: ${formatNumber(stats.totals.total_tokens)} tokens, ` +
         `${formatNumber(stats.totals.total_api_calls)} API calls, ` +
         `${formatNumber(stats.totals.total_tool_calls)} tool calls, ` +
         `${duration}${cacheInfo}`;
}
