/**
 * CLI-style formatters for HaiCode Agent
 * Provides beautiful terminal-style output formatting
 */

import type { SessionTokenStats, ModelTokenUsage, ToolCallStats } from '../types/tokenUsage.js';
import { formatDuration, formatNumber, formatPercentage } from './statsFormatter.js';

/**
 * ANSI color codes for terminal output
 */
export const Colors = {
  Reset: '\x1b[0m',
  Bright: '\x1b[1m',
  Dim: '\x1b[2m',
  
  // Foreground colors
  Red: '\x1b[31m',
  Green: '\x1b[32m',
  Yellow: '\x1b[33m',
  Blue: '\x1b[34m',
  Magenta: '\x1b[35m',
  <PERSON>an: '\x1b[36m',
  White: '\x1b[37m',
  Gray: '\x1b[90m',
  
  // Background colors
  BgRed: '\x1b[41m',
  BgGreen: '\x1b[42m',
  BgYellow: '\x1b[43m',
  BgBlue: '\x1b[44m',
  BgMagenta: '\x1b[45m',
  Bg<PERSON>yan: '\x1b[46m',
  BgWhite: '\x1b[47m',
};

/**
 * Check if colors should be used (based on environment)
 */
function shouldUseColors(): boolean {
  // Disable colors in CI or if NO_COLOR is set
  if (process.env.CI || process.env.NO_COLOR) {
    return false;
  }
  
  // Enable colors if FORCE_COLOR is set
  if (process.env.FORCE_COLOR) {
    return true;
  }
  
  // Check if stdout is a TTY
  return process.stdout.isTTY;
}

/**
 * Apply color to text if colors are enabled
 */
function colorize(text: string, color: string): string {
  return shouldUseColors() ? `${color}${text}${Colors.Reset}` : text;
}

/**
 * Create a section header
 */
function createSectionHeader(title: string, icon: string = '📊'): string {
  const header = `${icon} ${title}`;
  return colorize(header, Colors.Bright + Colors.Cyan);
}

/**
 * Create a sub-section header
 */
function createSubSectionHeader(title: string): string {
  return colorize(title, Colors.Bright + Colors.Blue);
}

/**
 * Create a stat row with label and value
 */
function createStatRow(label: string, value: string, indent: number = 0): string {
  const indentStr = '  '.repeat(indent);
  const coloredLabel = colorize(label, Colors.Gray);
  return `${indentStr}${coloredLabel} ${value}`;
}

/**
 * Create a divider line
 */
function createDivider(length: number = 50, char: string = '─'): string {
  return colorize(char.repeat(length), Colors.Gray);
}

/**
 * Format CLI-style session statistics
 */
export function formatCLISessionStats(stats: SessionTokenStats): string {
  const lines: string[] = [];
  
  // Header
  lines.push(createSectionHeader('Session Statistics', '📊'));
  lines.push(createDivider());
  lines.push('');
  
  // Session info
  lines.push(createSubSectionHeader('Session Info'));
  lines.push(createStatRow('Session ID:', colorize(stats.session_id, Colors.Yellow), 1));
  lines.push(createStatRow('Started:', stats.start_time.toLocaleString(), 1));
  
  if (stats.end_time) {
    lines.push(createStatRow('Ended:', stats.end_time.toLocaleString(), 1));
  }
  
  if (stats.duration_ms) {
    lines.push(createStatRow('Duration:', colorize(formatDuration(stats.duration_ms), Colors.Green), 1));
  }
  
  lines.push('');
  
  // Token summary
  lines.push(createSubSectionHeader('Token Usage'));
  lines.push(createStatRow('Total Tokens:', colorize(formatNumber(stats.totals.total_tokens), Colors.Yellow), 1));
  lines.push(createStatRow('Input Tokens:', colorize(formatNumber(stats.totals.total_input_tokens), Colors.Cyan), 1));
  lines.push(createStatRow('Output Tokens:', colorize(formatNumber(stats.totals.total_output_tokens), Colors.Magenta), 1));
  
  if (stats.totals.total_cached_tokens > 0) {
    lines.push(createStatRow('Cached Tokens:', colorize(formatNumber(stats.totals.total_cached_tokens), Colors.Green), 1));
    lines.push(createStatRow('Cache Efficiency:', colorize(formatPercentage(stats.totals.cache_efficiency), Colors.Green), 1));
  }
  
  lines.push('');
  
  // API calls
  lines.push(createSubSectionHeader('API Performance'));
  lines.push(createStatRow('API Calls:', colorize(formatNumber(stats.totals.total_api_calls), Colors.Blue), 1));
  
  if (stats.totals.total_api_latency_ms > 0) {
    const avgLatency = stats.totals.total_api_calls > 0 ? 
      stats.totals.total_api_latency_ms / stats.totals.total_api_calls : 0;
    lines.push(createStatRow('Total API Time:', formatDuration(stats.totals.total_api_latency_ms), 1));
    lines.push(createStatRow('Average Latency:', colorize(formatDuration(avgLatency), Colors.Yellow), 1));
  }
  
  // Tool usage
  if (stats.totals.total_tool_calls > 0) {
    lines.push('');
    lines.push(createSubSectionHeader('Tool Usage'));
    lines.push(createStatRow('Tool Calls:', colorize(formatNumber(stats.totals.total_tool_calls), Colors.Blue), 1));
    lines.push(createStatRow('Successful:', colorize(formatNumber(stats.totals.total_successful_tool_calls), Colors.Green), 1));
    lines.push(createStatRow('Success Rate:', colorize(formatPercentage(stats.totals.tool_success_rate), Colors.Green), 1));
    
    if (stats.totals.total_tool_duration_ms > 0) {
      lines.push(createStatRow('Total Tool Time:', formatDuration(stats.totals.total_tool_duration_ms), 1));
    }
  }
  
  return lines.join('\n');
}

/**
 * Format CLI-style model statistics
 */
export function formatCLIModelStats(models: Record<string, ModelTokenUsage>): string {
  const lines: string[] = [];
  
  lines.push(createSectionHeader('Model Statistics', '🤖'));
  lines.push(createDivider());
  lines.push('');
  
  const modelEntries = Object.entries(models);
  if (modelEntries.length === 0) {
    lines.push(colorize('No model usage data available.', Colors.Gray));
    return lines.join('\n');
  }
  
  // Sort by total tokens
  const sortedModels = modelEntries.sort(([, a], [, b]) => b.total_tokens - a.total_tokens);
  
  for (const [modelName, modelStats] of sortedModels) {
    lines.push(createSubSectionHeader(modelName));
    lines.push(createStatRow('Calls:', colorize(formatNumber(modelStats.call_count), Colors.Blue), 1));
    lines.push(createStatRow('Total Tokens:', colorize(formatNumber(modelStats.total_tokens), Colors.Yellow), 1));
    lines.push(createStatRow('Input Tokens:', colorize(formatNumber(modelStats.input_tokens), Colors.Cyan), 1));
    lines.push(createStatRow('Output Tokens:', colorize(formatNumber(modelStats.output_tokens), Colors.Magenta), 1));
    
    if (modelStats.cached_tokens && modelStats.cached_tokens > 0) {
      lines.push(createStatRow('Cached Tokens:', colorize(formatNumber(modelStats.cached_tokens), Colors.Green), 1));
    }
    
    if (modelStats.average_latency_ms > 0) {
      lines.push(createStatRow('Avg Latency:', colorize(formatDuration(modelStats.average_latency_ms), Colors.Yellow), 1));
    }
    
    lines.push('');
  }
  
  return lines.join('\n');
}

/**
 * Format CLI-style tool statistics
 */
export function formatCLIToolStats(tools: Record<string, ToolCallStats>): string {
  const lines: string[] = [];
  
  lines.push(createSectionHeader('Tool Statistics', '🔧'));
  lines.push(createDivider());
  lines.push('');
  
  const toolEntries = Object.entries(tools);
  if (toolEntries.length === 0) {
    lines.push(colorize('No tool usage data available.', Colors.Gray));
    return lines.join('\n');
  }
  
  // Sort by call count
  const sortedTools = toolEntries.sort(([, a], [, b]) => b.call_count - a.call_count);
  
  for (const [toolName, toolStats] of sortedTools) {
    lines.push(createSubSectionHeader(toolName));
    lines.push(createStatRow('Calls:', colorize(formatNumber(toolStats.call_count), Colors.Blue), 1));
    lines.push(createStatRow('Successful:', colorize(formatNumber(toolStats.success_count), Colors.Green), 1));
    lines.push(createStatRow('Failed:', colorize(formatNumber(toolStats.failure_count), Colors.Red), 1));
    
    const successColor = toolStats.success_rate >= 90 ? Colors.Green : 
                        toolStats.success_rate >= 70 ? Colors.Yellow : Colors.Red;
    lines.push(createStatRow('Success Rate:', colorize(formatPercentage(toolStats.success_rate), successColor), 1));
    
    if (toolStats.average_duration_ms > 0) {
      lines.push(createStatRow('Avg Duration:', colorize(formatDuration(toolStats.average_duration_ms), Colors.Yellow), 1));
    }
    
    lines.push('');
  }
  
  return lines.join('\n');
}

/**
 * Format CLI-style compact summary
 */
export function formatCLICompactSummary(stats: SessionTokenStats): string {
  const duration = stats.duration_ms ? formatDuration(stats.duration_ms) : 'ongoing';
  const cacheInfo = stats.totals.total_cached_tokens > 0 ? 
    ` (${formatPercentage(stats.totals.cache_efficiency)} cached)` : '';
  
  const sessionId = colorize(stats.session_id, Colors.Yellow);
  const tokens = colorize(formatNumber(stats.totals.total_tokens), Colors.Cyan);
  const apiCalls = colorize(formatNumber(stats.totals.total_api_calls), Colors.Blue);
  const toolCalls = colorize(formatNumber(stats.totals.total_tool_calls), Colors.Magenta);
  const durationStr = colorize(duration, Colors.Green);
  const cacheStr = cacheInfo ? colorize(cacheInfo, Colors.Green) : '';
  
  return `📋 Session ${sessionId}: ${tokens} tokens, ${apiCalls} API calls, ${toolCalls} tool calls, ${durationStr}${cacheStr}`;
}
