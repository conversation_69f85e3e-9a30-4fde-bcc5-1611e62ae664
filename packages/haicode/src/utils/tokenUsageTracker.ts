/**
 * Token usage tracking and aggregation utility
 * Provides comprehensive token usage statistics for HaiCode Agent
 */

import { logger } from './logger.js';
import type {
  TokenUsage,
  ModelTokenUsage,
  ToolCallStats,
  SessionTokenStats,
  TokenUsageEvent,
  TokenUsageConfig,
  TokenUsageCallback,
} from '../types/tokenUsage.js';

/**
 * Default configuration for token usage tracking
 */
export const DEFAULT_TOKEN_USAGE_CONFIG: TokenUsageConfig = {
  enabled: true,
  track_details: true,
  track_tools: true,
  track_per_model: true,
  max_sessions: 100,
  persist_stats: false,
};

/**
 * Token usage tracker class
 * Tracks and aggregates token usage across sessions, models, and tools
 */
export class TokenUsageTracker {
  private config: TokenUsageConfig;
  private sessions: Map<string, SessionTokenStats> = new Map();
  private callbacks: TokenUsageCallback[] = [];

  constructor(config: Partial<TokenUsageConfig> = {}) {
    this.config = { ...DEFAULT_TOKEN_USAGE_CONFIG, ...config };
    logger.debug('[TokenUsageTracker] Initialized with config:', { config: this.config });
  }

  /**
   * Add a callback to receive token usage events
   */
  addCallback(callback: TokenUsageCallback): void {
    this.callbacks.push(callback);
  }

  /**
   * Remove a callback
   */
  removeCallback(callback: TokenUsageCallback): void {
    const index = this.callbacks.indexOf(callback);
    if (index > -1) {
      this.callbacks.splice(index, 1);
    }
  }

  /**
   * Start tracking a new session
   */
  startSession(sessionId: string): void {
    if (!this.config.enabled) return;

    const sessionStats: SessionTokenStats = {
      session_id: sessionId,
      start_time: new Date(),
      models: {},
      tools: {},
      totals: {
        total_tokens: 0,
        total_input_tokens: 0,
        total_output_tokens: 0,
        total_cached_tokens: 0,
        total_api_calls: 0,
        total_tool_calls: 0,
        total_successful_tool_calls: 0,
        total_api_latency_ms: 0,
        total_tool_duration_ms: 0,
        cache_efficiency: 0,
        tool_success_rate: 0,
      },
    };

    this.sessions.set(sessionId, sessionStats);
    this.cleanupOldSessions();

    const event: TokenUsageEvent = {
      type: 'session_start',
      timestamp: new Date(),
      session_id: sessionId,
    };

    this.emitEvent(event);
    logger.debug(`[TokenUsageTracker] Started tracking session: ${sessionId}`);
  }

  /**
   * End tracking for a session
   */
  endSession(sessionId: string): void {
    if (!this.config.enabled) return;

    const session = this.sessions.get(sessionId);
    if (session) {
      session.end_time = new Date();
      session.duration_ms = session.end_time.getTime() - session.start_time.getTime();

      const event: TokenUsageEvent = {
        type: 'session_end',
        timestamp: new Date(),
        session_id: sessionId,
      };

      this.emitEvent(event);
      this.emitSessionUpdate(session);
      logger.debug(`[TokenUsageTracker] Ended tracking session: ${sessionId}`);
    }
  }

  /**
   * Record token usage from an LLM call
   */
  recordLLMCall(
    sessionId: string,
    model: string,
    tokenUsage: TokenUsage,
    durationMs: number,
    success: boolean = true,
    error?: string
  ): void {
    if (!this.config.enabled) return;

    let session = this.sessions.get(sessionId);
    if (!session) {
      this.startSession(sessionId);
      session = this.sessions.get(sessionId)!;
    }

    // Update model-specific statistics
    if (this.config.track_per_model) {
      this.updateModelStats(session, model, tokenUsage, durationMs);
    }

    // Update session totals
    this.updateSessionTotals(session, tokenUsage, durationMs, true);

    const event: TokenUsageEvent = {
      type: 'llm_call',
      timestamp: new Date(),
      session_id: sessionId,
      model,
      token_usage: tokenUsage,
      duration_ms: durationMs,
      success,
      error,
    };

    this.emitEvent(event);
    this.emitSessionUpdate(session);
  }

  /**
   * Record tool call statistics
   */
  recordToolCall(
    sessionId: string,
    toolName: string,
    durationMs: number,
    success: boolean,
    error?: string
  ): void {
    if (!this.config.enabled || !this.config.track_tools) return;

    let session = this.sessions.get(sessionId);
    if (!session) {
      this.startSession(sessionId);
      session = this.sessions.get(sessionId)!;
    }

    // Update tool-specific statistics
    this.updateToolStats(session, toolName, durationMs, success);

    // Update session totals
    session.totals.total_tool_calls++;
    if (success) {
      session.totals.total_successful_tool_calls++;
    }
    session.totals.total_tool_duration_ms += durationMs;
    session.totals.tool_success_rate = 
      (session.totals.total_successful_tool_calls / session.totals.total_tool_calls) * 100;

    const event: TokenUsageEvent = {
      type: 'tool_call',
      timestamp: new Date(),
      session_id: sessionId,
      tool_name: toolName,
      duration_ms: durationMs,
      success,
      error,
    };

    this.emitEvent(event);
    this.emitSessionUpdate(session);
  }

  /**
   * Get statistics for a specific session
   */
  getSessionStats(sessionId: string): SessionTokenStats | null {
    return this.sessions.get(sessionId) || null;
  }

  /**
   * Get statistics for all sessions
   */
  getAllSessionStats(): SessionTokenStats[] {
    return Array.from(this.sessions.values());
  }

  /**
   * Get aggregated statistics across all sessions
   */
  getAggregatedStats(): {
    total_sessions: number;
    active_sessions: number;
    total_tokens: number;
    total_api_calls: number;
    total_tool_calls: number;
    models: Record<string, ModelTokenUsage>;
    tools: Record<string, ToolCallStats>;
  } {
    const stats = {
      total_sessions: this.sessions.size,
      active_sessions: 0,
      total_tokens: 0,
      total_api_calls: 0,
      total_tool_calls: 0,
      models: {} as Record<string, ModelTokenUsage>,
      tools: {} as Record<string, ToolCallStats>,
    };

    for (const session of this.sessions.values()) {
      if (!session.end_time) {
        stats.active_sessions++;
      }

      stats.total_tokens += session.totals.total_tokens;
      stats.total_api_calls += session.totals.total_api_calls;
      stats.total_tool_calls += session.totals.total_tool_calls;

      // Aggregate model stats
      for (const [modelName, modelStats] of Object.entries(session.models)) {
        if (!stats.models[modelName]) {
          stats.models[modelName] = { ...modelStats };
        } else {
          this.mergeModelStats(stats.models[modelName], modelStats);
        }
      }

      // Aggregate tool stats
      for (const [toolName, toolStats] of Object.entries(session.tools)) {
        if (!stats.tools[toolName]) {
          stats.tools[toolName] = { ...toolStats };
        } else {
          this.mergeToolStats(stats.tools[toolName], toolStats);
        }
      }
    }

    return stats;
  }

  /**
   * Clear all session statistics
   */
  clearAllStats(): void {
    this.sessions.clear();
    logger.debug('[TokenUsageTracker] Cleared all session statistics');
  }

  /**
   * Clear statistics for a specific session
   */
  clearSessionStats(sessionId: string): void {
    this.sessions.delete(sessionId);
    logger.debug(`[TokenUsageTracker] Cleared statistics for session: ${sessionId}`);
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<TokenUsageConfig>): void {
    this.config = { ...this.config, ...newConfig };
    logger.debug('[TokenUsageTracker] Updated configuration:', { config: this.config });
  }

  /**
   * Get current configuration
   */
  getConfig(): TokenUsageConfig {
    return { ...this.config };
  }

  // Private helper methods

  private updateModelStats(
    session: SessionTokenStats,
    model: string,
    tokenUsage: TokenUsage,
    durationMs: number
  ): void {
    if (!session.models[model]) {
      session.models[model] = {
        model,
        input_tokens: 0,
        output_tokens: 0,
        total_tokens: 0,
        cached_tokens: 0,
        reasoning_tokens: 0,
        tool_tokens: 0,
        call_count: 0,
        total_latency_ms: 0,
        average_latency_ms: 0,
      };
    }

    const modelStats = session.models[model];
    modelStats.input_tokens += tokenUsage.input_tokens;
    modelStats.output_tokens += tokenUsage.output_tokens;
    modelStats.total_tokens += tokenUsage.total_tokens;
    modelStats.cached_tokens = (modelStats.cached_tokens || 0) + (tokenUsage.cached_tokens || 0);
    modelStats.reasoning_tokens = (modelStats.reasoning_tokens || 0) + (tokenUsage.reasoning_tokens || 0);
    modelStats.tool_tokens = (modelStats.tool_tokens || 0) + (tokenUsage.tool_tokens || 0);
    modelStats.call_count++;
    modelStats.total_latency_ms += durationMs;
    modelStats.average_latency_ms = modelStats.total_latency_ms / modelStats.call_count;
  }

  private updateToolStats(
    session: SessionTokenStats,
    toolName: string,
    durationMs: number,
    success: boolean
  ): void {
    if (!session.tools[toolName]) {
      session.tools[toolName] = {
        name: toolName,
        call_count: 0,
        success_count: 0,
        failure_count: 0,
        total_duration_ms: 0,
        average_duration_ms: 0,
        success_rate: 0,
      };
    }

    const toolStats = session.tools[toolName];
    toolStats.call_count++;
    if (success) {
      toolStats.success_count++;
    } else {
      toolStats.failure_count++;
    }
    toolStats.total_duration_ms += durationMs;
    toolStats.average_duration_ms = toolStats.total_duration_ms / toolStats.call_count;
    toolStats.success_rate = (toolStats.success_count / toolStats.call_count) * 100;
  }

  private updateSessionTotals(
    session: SessionTokenStats,
    tokenUsage: TokenUsage,
    durationMs: number,
    isApiCall: boolean
  ): void {
    session.totals.total_tokens += tokenUsage.total_tokens;
    session.totals.total_input_tokens += tokenUsage.input_tokens;
    session.totals.total_output_tokens += tokenUsage.output_tokens;
    session.totals.total_cached_tokens += tokenUsage.cached_tokens || 0;

    if (isApiCall) {
      session.totals.total_api_calls++;
      session.totals.total_api_latency_ms += durationMs;
    }

    // Calculate cache efficiency
    if (session.totals.total_input_tokens > 0) {
      session.totals.cache_efficiency =
        (session.totals.total_cached_tokens / session.totals.total_input_tokens) * 100;
    }
  }

  private mergeModelStats(target: ModelTokenUsage, source: ModelTokenUsage): void {
    target.input_tokens += source.input_tokens;
    target.output_tokens += source.output_tokens;
    target.total_tokens += source.total_tokens;
    target.cached_tokens = (target.cached_tokens || 0) + (source.cached_tokens || 0);
    target.reasoning_tokens = (target.reasoning_tokens || 0) + (source.reasoning_tokens || 0);
    target.tool_tokens = (target.tool_tokens || 0) + (source.tool_tokens || 0);
    target.call_count += source.call_count;
    target.total_latency_ms += source.total_latency_ms;
    target.average_latency_ms = target.total_latency_ms / target.call_count;
  }

  private mergeToolStats(target: ToolCallStats, source: ToolCallStats): void {
    target.call_count += source.call_count;
    target.success_count += source.success_count;
    target.failure_count += source.failure_count;
    target.total_duration_ms += source.total_duration_ms;
    target.average_duration_ms = target.total_duration_ms / target.call_count;
    target.success_rate = (target.success_count / target.call_count) * 100;
  }

  private cleanupOldSessions(): void {
    if (this.sessions.size <= this.config.max_sessions) return;

    // Sort sessions by start time and remove oldest ones
    const sortedSessions = Array.from(this.sessions.entries())
      .sort(([, a], [, b]) => a.start_time.getTime() - b.start_time.getTime());

    const sessionsToRemove = sortedSessions.slice(0, this.sessions.size - this.config.max_sessions);

    for (const [sessionId] of sessionsToRemove) {
      this.sessions.delete(sessionId);
    }

    logger.debug(`[TokenUsageTracker] Cleaned up ${sessionsToRemove.length} old sessions`);
  }

  private emitEvent(event: TokenUsageEvent): void {
    for (const callback of this.callbacks) {
      try {
        callback.onTokenUsage?.(event);
      } catch (error) {
        logger.error('[TokenUsageTracker] Error in token usage callback:', { error: error instanceof Error ? error.message : String(error) });
        callback.onError?.(error instanceof Error ? error : new Error(String(error)));
      }
    }
  }

  private emitSessionUpdate(session: SessionTokenStats): void {
    for (const callback of this.callbacks) {
      try {
        callback.onSessionUpdate?.(session);
      } catch (error) {
        logger.error('[TokenUsageTracker] Error in session update callback:', { error: error instanceof Error ? error.message : String(error) });
        callback.onError?.(error instanceof Error ? error : new Error(String(error)));
      }
    }
  }
}
