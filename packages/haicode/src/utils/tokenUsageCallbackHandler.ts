/**
 * Custom callback handler for token usage tracking in LangChain/LangGraph
 * Integrates with TokenUsageTracker to provide comprehensive statistics
 */

import { BaseCallbackHandler } from '@langchain/core/callbacks/base';
import type { LLMResult } from '@langchain/core/outputs';
import type { BaseMessage } from '@langchain/core/messages';
import type { AgentAction, AgentFinish } from '@langchain/core/agents';
import type { Document } from '@langchain/core/documents';
import type { ChainValues } from '@langchain/core/utils/types';
import type { Serialized } from '@langchain/core/load/serializable';
import { logger } from './logger.js';
import type { TokenUsageTracker } from './tokenUsageTracker.js';
import type { TokenUsage } from '../types/tokenUsage.js';

/**
 * Callback handler for tracking token usage in LangChain/LangGraph workflows
 */
export class TokenUsageCallbackHandler extends BaseCallbackHandler {
  name = 'TokenUsageCallbackHandler';
  
  private tracker: TokenUsageTracker;
  private sessionId: string;
  private callStartTimes: Map<string, number> = new Map();
  private toolStartTimes: Map<string, number> = new Map();

  constructor(tracker: TokenUsageTracker, sessionId: string) {
    super();
    this.tracker = tracker;
    this.sessionId = sessionId;
  }

  /**
   * Called when an LLM starts running
   */
  async handleLLMStart(
    llm: Serialized,
    prompts: string[],
    runId: string,
    parentRunId?: string,
    extraParams?: Record<string, unknown>,
    tags?: string[],
    metadata?: Record<string, unknown>,
    runName?: string
  ): Promise<void> {
    this.callStartTimes.set(runId, Date.now());
    logger.debug(`[TokenUsageCallbackHandler] LLM call started: ${runId}`, {
      llmType: (llm as any)._llmType || llm.id?.[llm.id.length - 1] || 'unknown',
      promptCount: prompts.length,
      sessionId: this.sessionId,
    });
  }

  /**
   * Called when an LLM ends running
   */
  async handleLLMEnd(
    output: LLMResult,
    runId: string,
    parentRunId?: string,
    tags?: string[]
  ): Promise<void> {
    const startTime = this.callStartTimes.get(runId);
    const durationMs = startTime ? Date.now() - startTime : 0;
    this.callStartTimes.delete(runId);

    // Extract token usage from LLM result
    const tokenUsage = this.extractTokenUsage(output);
    if (tokenUsage) {
      // Try to determine the model name from the output
      const model = this.extractModelName(output) || 'unknown';
      
      this.tracker.recordLLMCall(
        this.sessionId,
        model,
        tokenUsage,
        durationMs,
        true // success
      );

      logger.debug(`[TokenUsageCallbackHandler] LLM call completed: ${runId}`, {
        model,
        tokenUsage,
        durationMs,
        sessionId: this.sessionId,
      });
    }
  }

  /**
   * Called when an LLM errors
   */
  async handleLLMError(
    err: Error,
    runId: string,
    parentRunId?: string,
    tags?: string[]
  ): Promise<void> {
    const startTime = this.callStartTimes.get(runId);
    const durationMs = startTime ? Date.now() - startTime : 0;
    this.callStartTimes.delete(runId);

    logger.warn(`[TokenUsageCallbackHandler] LLM call failed: ${runId}`, {
      error: err.message,
      durationMs,
      sessionId: this.sessionId,
    });

    // Record failed call with zero token usage
    this.tracker.recordLLMCall(
      this.sessionId,
      'unknown',
      { input_tokens: 0, output_tokens: 0, total_tokens: 0 },
      durationMs,
      false,
      err.message
    );
  }

  /**
   * Called when a tool starts running
   */
  async handleToolStart(
    tool: Serialized,
    input: string,
    runId: string,
    parentRunId?: string,
    tags?: string[],
    metadata?: Record<string, unknown>,
    runName?: string
  ): Promise<void> {
    this.toolStartTimes.set(runId, Date.now());
    const toolName = (tool as any).name || tool.id?.[tool.id.length - 1] || 'unknown_tool';
    logger.debug(`[TokenUsageCallbackHandler] Tool call started: ${toolName}`, {
      runId,
      sessionId: this.sessionId,
    });
  }

  /**
   * Called when a tool ends running
   */
  async handleToolEnd(
    output: string,
    runId: string,
    parentRunId?: string,
    tags?: string[]
  ): Promise<void> {
    const startTime = this.toolStartTimes.get(runId);
    const durationMs = startTime ? Date.now() - startTime : 0;
    this.toolStartTimes.delete(runId);

    // We need to get the tool name from somewhere - this is a limitation of the callback interface
    // For now, we'll use a generic name and try to extract it from tags or metadata
    const toolName = this.extractToolName(tags) || 'unknown_tool';

    this.tracker.recordToolCall(
      this.sessionId,
      toolName,
      durationMs,
      true // success
    );

    logger.debug(`[TokenUsageCallbackHandler] Tool call completed: ${toolName}`, {
      runId,
      durationMs,
      sessionId: this.sessionId,
    });
  }

  /**
   * Called when a tool errors
   */
  async handleToolError(
    err: Error,
    runId: string,
    parentRunId?: string,
    tags?: string[]
  ): Promise<void> {
    const startTime = this.toolStartTimes.get(runId);
    const durationMs = startTime ? Date.now() - startTime : 0;
    this.toolStartTimes.delete(runId);

    const toolName = this.extractToolName(tags) || 'unknown_tool';

    this.tracker.recordToolCall(
      this.sessionId,
      toolName,
      durationMs,
      false,
      err.message
    );

    logger.warn(`[TokenUsageCallbackHandler] Tool call failed: ${toolName}`, {
      runId,
      error: err.message,
      durationMs,
      sessionId: this.sessionId,
    });
  }

  /**
   * Called when an agent action is taken
   */
  async handleAgentAction(
    action: AgentAction,
    runId: string,
    parentRunId?: string,
    tags?: string[]
  ): Promise<void> {
    // This can be used to track agent-specific actions if needed
    logger.debug(`[TokenUsageCallbackHandler] Agent action: ${action.tool}`, {
      runId,
      sessionId: this.sessionId,
    });
  }

  /**
   * Called when an agent finishes
   */
  async handleAgentEnd(
    action: AgentFinish,
    runId: string,
    parentRunId?: string,
    tags?: string[]
  ): Promise<void> {
    logger.debug(`[TokenUsageCallbackHandler] Agent finished`, {
      runId,
      sessionId: this.sessionId,
    });
  }

  // Private helper methods

  /**
   * Extract token usage from LLM result
   */
  private extractTokenUsage(output: LLMResult): TokenUsage | null {
    try {
      // Check for usage metadata in the LLM output
      if (output.llmOutput?.tokenUsage) {
        const usage = output.llmOutput.tokenUsage;
        return {
          input_tokens: usage.promptTokens || 0,
          output_tokens: usage.completionTokens || 0,
          total_tokens: usage.totalTokens || 0,
          cached_tokens: usage.cachedTokens,
          reasoning_tokens: usage.reasoningTokens,
          tool_tokens: usage.toolTokens,
        };
      }

      // Check for usage metadata in generations
      if (output.generations?.[0]?.[0]?.generationInfo?.usage) {
        const usage = output.generations[0][0].generationInfo.usage;
        return {
          input_tokens: usage.input_tokens || usage.promptTokens || 0,
          output_tokens: usage.output_tokens || usage.completionTokens || 0,
          total_tokens: usage.total_tokens || usage.totalTokens || 0,
          cached_tokens: usage.cached_tokens || usage.cachedTokens,
          reasoning_tokens: usage.reasoning_tokens || usage.reasoningTokens,
          tool_tokens: usage.tool_tokens || usage.toolTokens,
        };
      }

      // Check for usage metadata in message response_metadata
      const generation = output.generations?.[0]?.[0];
      const generationWithMessage = generation as any;
      if (generationWithMessage?.message?.response_metadata?.tokenUsage) {
        const usage = generationWithMessage.message.response_metadata.tokenUsage;
        return {
          input_tokens: usage.promptTokens || 0,
          output_tokens: usage.completionTokens || 0,
          total_tokens: usage.totalTokens || 0,
          cached_tokens: usage.cachedTokens,
          reasoning_tokens: usage.reasoningTokens,
          tool_tokens: usage.toolTokens,
        };
      }

      // Check for usage_metadata in message (LangChain.js standard)
      if (generationWithMessage?.message?.usage_metadata) {
        const usage = generationWithMessage.message.usage_metadata;
        return {
          input_tokens: usage.input_tokens || 0,
          output_tokens: usage.output_tokens || 0,
          total_tokens: usage.total_tokens || 0,
          cached_tokens: usage.cached_tokens,
          reasoning_tokens: usage.reasoning_tokens,
          tool_tokens: usage.tool_tokens,
          input_token_details: usage.input_token_details,
          output_token_details: usage.output_token_details,
        };
      }

      return null;
    } catch (error) {
      logger.warn('[TokenUsageCallbackHandler] Failed to extract token usage:', { error: error instanceof Error ? error.message : String(error) });
      return null;
    }
  }

  /**
   * Extract model name from LLM result
   */
  private extractModelName(output: LLMResult): string | null {
    try {
      // Try to get model name from various possible locations
      if (output.llmOutput?.model) {
        return output.llmOutput.model;
      }

      if (output.llmOutput?.modelName) {
        return output.llmOutput.modelName;
      }

      const generation = output.generations?.[0]?.[0];
      const generationWithMessage = generation as any;
      if (generationWithMessage?.message?.response_metadata?.model) {
        return generationWithMessage.message.response_metadata.model;
      }

      if (generationWithMessage?.message?.response_metadata?.model_name) {
        return generationWithMessage.message.response_metadata.model_name;
      }

      return null;
    } catch (error) {
      logger.warn('[TokenUsageCallbackHandler] Failed to extract model name:', { error: error instanceof Error ? error.message : String(error) });
      return null;
    }
  }

  /**
   * Extract tool name from tags
   */
  private extractToolName(tags?: string[]): string | null {
    if (!tags) return null;
    
    // Look for tool name in tags
    for (const tag of tags) {
      if (tag.startsWith('tool:')) {
        return tag.substring(5);
      }
    }
    
    return null;
  }
}
