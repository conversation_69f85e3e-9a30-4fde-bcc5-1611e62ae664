import { HaiCodeAgent } from '../index.js';
import chalk from 'chalk';
import { logger } from './logger.js';

export async function runInteractiveMode(agent: HaiCodeAgent, initialSessionId: string): Promise<void> {
  console.log(chalk.cyan('🤖 Hai Code Agent - Interactive mode'));
  console.log(chalk.gray('Type "exit" or "quit" to exit, Ctrl+C to quit immediately'));
  console.log(chalk.gray('Use "/sessions" to manage conversation sessions'));
  console.log('');

  // Use readline for interactive input
  const readline = await import('node:readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
    prompt: chalk.blue('> '),
  });

  // Track current session ID (can be changed by /sessions select command)
  let currentSessionId = initialSessionId;

  rl.prompt();

  rl.on('line', async (input) => {
    const trimmed = input.trim();

    if (trimmed === 'exit' || trimmed === 'quit') {
      rl.close();
      return;
    }

    if (trimmed === '') {
      rl.prompt();
      return;
    }

    try {
      console.log(''); // Add space before response

      // Store the session ID before processing the message
      const previousSessionId = currentSessionId;

      // Process the message
      for await (const chunk of agent.streamMessage(trimmed, currentSessionId)) {
        process.stdout.write(chunk);
      }

      // Check if the session was changed by a command (e.g., /sessions select)
      const newSessionId = agent.getCurrentSessionId();
      if (newSessionId && newSessionId !== previousSessionId) {
        currentSessionId = newSessionId;
        console.log(chalk.green(`\n🔄 Session switched to: ${currentSessionId.substring(0, 8)}...`));
      }

      console.log('\n'); // Add newline after response
    } catch (error) {
      logger.error('交互模式处理消息时出错', { error: error instanceof Error ? error.message : String(error) });
    }

    rl.prompt();
  });

  rl.on('close', async () => {
    console.log(chalk.gray('\nGoodbye! 👋'));
    await agent.flushLangfuse();
    process.exit(0);
  });

  // Handle Ctrl+C
  process.on('SIGINT', async () => {
    console.log(chalk.gray('\nGoodbye! 👋'));
    await agent.flushLangfuse();
    process.exit(0);
  });
}
