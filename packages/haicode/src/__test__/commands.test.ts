/**
 * Unit tests for command processing functionality
 */

import { describe, it, beforeEach, afterEach, expect } from 'vitest';
import { parseCommand, isCommand, validateCommand } from '../commands/parser.js';
import { DefaultCommandRegistry } from '../commands/registry.js';
import { CommandProcessor } from '../commands/processor.js';
import { TokenUsageTracker } from '../utils/tokenUsageTracker.js';
import type { CommandContext } from '../commands/types.js';

describe('Command Parser', () => {
  describe('isCommand', () => {
    it('should identify commands correctly', () => {
      expect(isCommand('/stats')).toBe(true);
      expect(isCommand('/help')).toBe(true);
      expect(isCommand('  /clear  ')).toBe(true);
      expect(isCommand('hello world')).toBe(false);
      expect(isCommand('')).toBe(false);
      expect(isCommand('stats')).toBe(false);
    });
  });

  describe('parseCommand', () => {
    it('should parse simple commands', () => {
      const result = parseCommand('/stats');
      expect(result.command).toBe('stats');
      expect(result.subCommand).toBeUndefined();
      expect(result.args).toEqual([]);
      expect(result.original).toBe('/stats');
    });

    it('should parse commands with sub-commands', () => {
      const result = parseCommand('/stats model');
      expect(result.command).toBe('stats');
      expect(result.subCommand).toBe('model');
      expect(result.args).toEqual([]);
    });

    it('should parse commands with arguments', () => {
      const result = parseCommand('/help stats');
      expect(result.command).toBe('help');
      expect(result.subCommand).toBeUndefined();
      expect(result.args).toEqual(['stats']);
    });

    it('should handle extra whitespace', () => {
      const result = parseCommand('  /stats   model   ');
      expect(result.command).toBe('stats');
      expect(result.subCommand).toBe('model');
      expect(result.args).toEqual([]);
    });

    it('should throw error for invalid commands', () => {
      expect(() => parseCommand('stats')).toThrow('Invalid command: must start with /');
      expect(() => parseCommand('/')).toThrow('Invalid command: empty command');
    });
  });

  describe('validateCommand', () => {
    it('should validate known commands', () => {
      expect(validateCommand(parseCommand('/stats'))).toEqual({ valid: true });
      expect(validateCommand(parseCommand('/help'))).toEqual({ valid: true });
      expect(validateCommand(parseCommand('/clear'))).toEqual({ valid: true });
    });

    it('should validate stats sub-commands', () => {
      expect(validateCommand(parseCommand('/stats model'))).toEqual({ valid: true });
      expect(validateCommand(parseCommand('/stats tools'))).toEqual({ valid: true });
      expect(validateCommand(parseCommand('/stats session'))).toEqual({ valid: true });
    });

    it('should reject unknown commands', () => {
      const result = validateCommand(parseCommand('/unknown'));
      expect(result.valid).toBe(false);
      expect(result.error).toContain('Unknown command');
    });

    it('should reject invalid sub-commands', () => {
      const result = validateCommand(parseCommand('/stats invalid'));
      expect(result.valid).toBe(false);
      expect(result.error).toContain('Invalid stats sub-command');
    });
  });
});

describe('Command Registry', () => {
  let registry: DefaultCommandRegistry;

  beforeEach(() => {
    registry = new DefaultCommandRegistry();
  });

  it('should register and retrieve commands', () => {
    const command = registry.get('stats');
    expect(command).toBeDefined();
    expect(command?.name).toBe('stats');
  });

  it('should handle command aliases', () => {
    const helpCommand = registry.get('help');
    const hCommand = registry.get('h');
    const questionCommand = registry.get('?');
    
    expect(helpCommand).toBeDefined();
    expect(hCommand).toBeDefined();
    expect(questionCommand).toBeDefined();
    expect(helpCommand?.name).toBe('help');
    expect(hCommand?.name).toBe('help');
    expect(questionCommand?.name).toBe('help');
  });

  it('should return all registered commands', () => {
    const commands = registry.getAll();
    expect(commands.length).toBeGreaterThan(0);
    
    const commandNames = commands.map(cmd => cmd.name);
    expect(commandNames).toContain('stats');
    expect(commandNames).toContain('help');
    expect(commandNames).toContain('clear');
  });
});

describe('Command Processor', () => {
  let processor: CommandProcessor;
  let mockAgent: any;
  let context: CommandContext;

  beforeEach(() => {
    processor = new CommandProcessor();
    
    // Create a mock agent with token usage functionality
    mockAgent = {
      getTokenUsageStats: () => ({
        session_id: 'test-session',
        start_time: new Date(),
        models: {
          'test-model': {
            model: 'test-model',
            input_tokens: 100,
            output_tokens: 50,
            total_tokens: 150,
            call_count: 1,
            total_latency_ms: 1000,
            average_latency_ms: 1000,
          },
        },
        tools: {
          'test-tool': {
            name: 'test-tool',
            call_count: 2,
            success_count: 2,
            failure_count: 0,
            total_duration_ms: 500,
            average_duration_ms: 250,
            success_rate: 100,
          },
        },
        totals: {
          total_tokens: 150,
          total_input_tokens: 100,
          total_output_tokens: 50,
          total_cached_tokens: 0,
          total_api_calls: 1,
          total_tool_calls: 2,
          total_successful_tool_calls: 2,
          total_api_latency_ms: 1000,
          total_tool_duration_ms: 500,
          cache_efficiency: 0,
          tool_success_rate: 100,
        },
      }),
      getFormattedAggregatedStats: () => 'Aggregated stats',
      clearTokenUsageStats: () => {},
    };

    context = {
      sessionId: 'test-session',
      agent: mockAgent,
    };
  });

  it('should identify commands correctly', () => {
    expect(processor.isCommand('/stats')).toBe(true);
    expect(processor.isCommand('hello')).toBe(false);
  });

  it('should process help command', async () => {
    const result = await processor.processCommand('/help', context);
    expect(result.type).toBe('message');
    expect(result.content).toContain('Available Commands');
  });

  it('should process stats command', async () => {
    const result = await processor.processCommand('/stats', context);
    expect(result.type).toBe('stats');
    expect(result.content).toContain('Session Statistics');
    expect(result.data?.stats).toBeDefined();
  });

  it('should process stats model sub-command', async () => {
    const result = await processor.processCommand('/stats model', context);
    expect(result.type).toBe('stats');
    expect(result.content).toContain('Model Statistics');
  });

  it('should process stats tools sub-command', async () => {
    const result = await processor.processCommand('/stats tools', context);
    expect(result.type).toBe('stats');
    expect(result.content).toContain('Tool Statistics');
  });

  it('should process clear command', async () => {
    const result = await processor.processCommand('/clear', context);
    expect(result.type).toBe('message');
    expect(result.content).toContain('cleared');
  });

  it('should handle unknown commands', async () => {
    const result = await processor.processCommand('/unknown', context);
    expect(result.type).toBe('error');
    expect(result.content).toContain('Unknown command');
  });

  it('should handle invalid command syntax', async () => {
    const result = await processor.processCommand('/stats invalid', context);
    expect(result.type).toBe('error');
    expect(result.content).toContain('Invalid stats sub-command');
  });

  it('should handle non-command messages', async () => {
    try {
      await processor.processCommand('hello world', context);
      expect.fail('Should have thrown an error');
    } catch (error) {
      expect(error).toBeInstanceOf(Error);
      expect((error as Error).message).toContain('Message is not a command');
    }
  });
});

describe('Command Integration', () => {
  let tracker: TokenUsageTracker;
  let processor: CommandProcessor;
  let mockAgent: any;
  let context: CommandContext;

  beforeEach(() => {
    tracker = new TokenUsageTracker({
      enabled: true,
      track_details: true,
      track_tools: true,
      track_per_model: true,
      max_sessions: 5,
      persist_stats: false,
    });

    // Start a session and add some data
    tracker.startSession('test-session');
    tracker.recordLLMCall('test-session', 'test-model', {
      input_tokens: 100,
      output_tokens: 50,
      total_tokens: 150,
    }, 1000, true);
    tracker.recordToolCall('test-session', 'test-tool', 500, true);

    mockAgent = {
      getTokenUsageStats: (sessionId: string) => tracker.getSessionStats(sessionId),
      getFormattedAggregatedStats: () => 'Aggregated stats',
      clearTokenUsageStats: (sessionId: string) => tracker.clearSessionStats(sessionId),
    };

    processor = new CommandProcessor();
    context = {
      sessionId: 'test-session',
      agent: mockAgent,
    };
  });

  afterEach(() => {
    tracker.clearAllStats();
  });

  it('should show real statistics data', async () => {
    const result = await processor.processCommand('/stats', context);
    expect(result.type).toBe('stats');
    expect(result.content).toContain('150'); // total tokens
    // The basic stats command shows session info, not model details
    expect(result.content).toContain('Session Statistics');
  });

  it('should clear statistics when requested', async () => {
    // Verify stats exist
    let stats = tracker.getSessionStats('test-session');
    expect(stats?.totals.total_tokens).toBe(150);

    // Clear stats
    await processor.processCommand('/clear', context);

    // Verify stats are cleared
    stats = tracker.getSessionStats('test-session');
    expect(stats).toBeNull();
  });
});
