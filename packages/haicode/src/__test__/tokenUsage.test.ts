/**
 * Unit tests for token usage tracking functionality
 */

import { describe, it, beforeEach, afterEach, expect } from 'vitest';
import { TokenUsageTracker } from '../utils/tokenUsageTracker.js';
import { TokenUsageCallbackHandler } from '../utils/tokenUsageCallbackHandler.js';
import { formatSessionStats, formatDuration, formatNumber } from '../utils/statsFormatter.js';
import type { TokenUsage, TokenUsageEvent, SessionTokenStats } from '../types/tokenUsage.js';

describe('TokenUsageTracker', () => {
  let tracker: TokenUsageTracker;

  beforeEach(() => {
    tracker = new TokenUsageTracker({
      enabled: true,
      track_details: true,
      track_tools: true,
      track_per_model: true,
      max_sessions: 5,
      persist_stats: false,
    });
  });

  afterEach(() => {
    tracker.clearAllStats();
  });

  describe('Session Management', () => {
    it('should start and track a new session', () => {
      const sessionId = 'test-session-1';
      tracker.startSession(sessionId);

      const stats = tracker.getSessionStats(sessionId);
      expect(stats).toBeDefined();
      expect(stats?.session_id).toBe(sessionId);
      expect(stats?.start_time).toBeInstanceOf(Date);
      expect(stats?.end_time).toBeUndefined();
    });

    it('should end a session and record end time', async () => {
      const sessionId = 'test-session-2';
      tracker.startSession(sessionId);

      // Wait a bit to ensure duration > 0
      await new Promise(resolve => setTimeout(resolve, 10));

      tracker.endSession(sessionId);

      const stats = tracker.getSessionStats(sessionId);
      expect(stats?.end_time).toBeInstanceOf(Date);
      expect(stats?.duration_ms).toBeGreaterThan(0);
    });

    it('should handle multiple sessions', () => {
      const sessionIds = ['session-1', 'session-2', 'session-3'];
      
      sessionIds.forEach(id => tracker.startSession(id));
      
      const allStats = tracker.getAllSessionStats();
      expect(allStats).toHaveLength(3);
      
      sessionIds.forEach(id => {
        const stats = tracker.getSessionStats(id);
        expect(stats?.session_id).toBe(id);
      });
    });
  });

  describe('LLM Call Tracking', () => {
    it('should record LLM call statistics', () => {
      const sessionId = 'llm-test-session';
      const model = 'test-model';
      const tokenUsage: TokenUsage = {
        input_tokens: 100,
        output_tokens: 50,
        total_tokens: 150,
        cached_tokens: 20,
      };
      const durationMs = 1500;

      tracker.startSession(sessionId);
      tracker.recordLLMCall(sessionId, model, tokenUsage, durationMs, true);

      const stats = tracker.getSessionStats(sessionId);
      expect(stats?.totals.total_tokens).toBe(150);
      expect(stats?.totals.total_input_tokens).toBe(100);
      expect(stats?.totals.total_output_tokens).toBe(50);
      expect(stats?.totals.total_cached_tokens).toBe(20);
      expect(stats?.totals.total_api_calls).toBe(1);
      expect(stats?.totals.total_api_latency_ms).toBe(1500);

      expect(stats?.models[model]).toBeDefined();
      expect(stats?.models[model].call_count).toBe(1);
      expect(stats?.models[model].total_tokens).toBe(150);
      expect(stats?.models[model].average_latency_ms).toBe(1500);
    });

    it('should aggregate multiple LLM calls', () => {
      const sessionId = 'multi-llm-session';
      const model = 'test-model';

      tracker.startSession(sessionId);
      
      // First call
      tracker.recordLLMCall(sessionId, model, {
        input_tokens: 100,
        output_tokens: 50,
        total_tokens: 150,
      }, 1000, true);

      // Second call
      tracker.recordLLMCall(sessionId, model, {
        input_tokens: 200,
        output_tokens: 100,
        total_tokens: 300,
      }, 2000, true);

      const stats = tracker.getSessionStats(sessionId);
      expect(stats?.totals.total_tokens).toBe(450);
      expect(stats?.totals.total_api_calls).toBe(2);
      expect(stats?.models[model].call_count).toBe(2);
      expect(stats?.models[model].total_tokens).toBe(450);
      expect(stats?.models[model].average_latency_ms).toBe(1500); // (1000 + 2000) / 2
    });
  });

  describe('Tool Call Tracking', () => {
    it('should record tool call statistics', () => {
      const sessionId = 'tool-test-session';
      const toolName = 'test-tool';
      const durationMs = 500;

      tracker.startSession(sessionId);
      tracker.recordToolCall(sessionId, toolName, durationMs, true);

      const stats = tracker.getSessionStats(sessionId);
      expect(stats?.totals.total_tool_calls).toBe(1);
      expect(stats?.totals.total_successful_tool_calls).toBe(1);
      expect(stats?.totals.tool_success_rate).toBe(100);
      expect(stats?.totals.total_tool_duration_ms).toBe(500);

      expect(stats?.tools[toolName]).toBeDefined();
      expect(stats?.tools[toolName].call_count).toBe(1);
      expect(stats?.tools[toolName].success_count).toBe(1);
      expect(stats?.tools[toolName].failure_count).toBe(0);
      expect(stats?.tools[toolName].success_rate).toBe(100);
    });

    it('should handle failed tool calls', () => {
      const sessionId = 'tool-fail-session';
      const toolName = 'failing-tool';

      tracker.startSession(sessionId);
      tracker.recordToolCall(sessionId, toolName, 300, false, 'Tool error');

      const stats = tracker.getSessionStats(sessionId);
      expect(stats?.totals.total_tool_calls).toBe(1);
      expect(stats?.totals.total_successful_tool_calls).toBe(0);
      expect(stats?.totals.tool_success_rate).toBe(0);

      expect(stats?.tools[toolName].success_count).toBe(0);
      expect(stats?.tools[toolName].failure_count).toBe(1);
      expect(stats?.tools[toolName].success_rate).toBe(0);
    });
  });

  describe('Aggregated Statistics', () => {
    it('should provide aggregated statistics across sessions', () => {
      const session1 = 'agg-session-1';
      const session2 = 'agg-session-2';
      const model = 'test-model';

      tracker.startSession(session1);
      tracker.recordLLMCall(session1, model, {
        input_tokens: 100,
        output_tokens: 50,
        total_tokens: 150,
      }, 1000, true);

      tracker.startSession(session2);
      tracker.recordLLMCall(session2, model, {
        input_tokens: 200,
        output_tokens: 100,
        total_tokens: 300,
      }, 2000, true);

      const aggregated = tracker.getAggregatedStats();
      expect(aggregated.total_sessions).toBe(2);
      expect(aggregated.active_sessions).toBe(2);
      expect(aggregated.total_tokens).toBe(450);
      expect(aggregated.total_api_calls).toBe(2);

      expect(aggregated.models[model]).toBeDefined();
      expect(aggregated.models[model].call_count).toBe(2);
      expect(aggregated.models[model].total_tokens).toBe(450);
    });
  });

  describe('Callback System', () => {
    it('should call callbacks on events', async () => {
      const sessionId = 'callback-session';
      let eventReceived = false;
      let sessionUpdateReceived = false;

      tracker.addCallback({
        onTokenUsage: (event: TokenUsageEvent) => {
          // The first event will be session_start, then llm_call
          if (event.type === 'llm_call') {
            expect(event.session_id).toBe(sessionId);
            eventReceived = true;
          }
        },
        onSessionUpdate: (stats: SessionTokenStats) => {
          expect(stats.session_id).toBe(sessionId);
          sessionUpdateReceived = true;
        },
      });

      tracker.startSession(sessionId);
      tracker.recordLLMCall(sessionId, 'test-model', {
        input_tokens: 10,
        output_tokens: 5,
        total_tokens: 15,
      }, 100, true);

      // Wait a bit for callbacks to be called
      await new Promise(resolve => setTimeout(resolve, 10));

      expect(eventReceived).toBe(true);
      expect(sessionUpdateReceived).toBe(true);
    });
  });
});

describe('Stats Formatter', () => {
  describe('formatDuration', () => {
    it('should format milliseconds correctly', () => {
      expect(formatDuration(500)).toBe('500ms');
      expect(formatDuration(1500)).toBe('1s');
      expect(formatDuration(65000)).toBe('1m 5s');
      expect(formatDuration(3665000)).toBe('1h 1m 5s');
    });
  });

  describe('formatNumber', () => {
    it('should format numbers with thousands separators', () => {
      expect(formatNumber(1000)).toBe('1,000');
      expect(formatNumber(1234567)).toBe('1,234,567');
    });
  });

  describe('formatSessionStats', () => {
    it('should format session statistics correctly', () => {
      const stats: SessionTokenStats = {
        session_id: 'test-session',
        start_time: new Date('2024-01-01T10:00:00Z'),
        end_time: new Date('2024-01-01T10:05:00Z'),
        duration_ms: 300000,
        models: {
          'test-model': {
            model: 'test-model',
            input_tokens: 100,
            output_tokens: 50,
            total_tokens: 150,
            call_count: 1,
            total_latency_ms: 1000,
            average_latency_ms: 1000,
          },
        },
        tools: {
          'test-tool': {
            name: 'test-tool',
            call_count: 2,
            success_count: 2,
            failure_count: 0,
            total_duration_ms: 500,
            average_duration_ms: 250,
            success_rate: 100,
          },
        },
        totals: {
          total_tokens: 150,
          total_input_tokens: 100,
          total_output_tokens: 50,
          total_cached_tokens: 0,
          total_api_calls: 1,
          total_tool_calls: 2,
          total_successful_tool_calls: 2,
          total_api_latency_ms: 1000,
          total_tool_duration_ms: 500,
          cache_efficiency: 0,
          tool_success_rate: 100,
        },
      };

      const formatted = formatSessionStats(stats);
      expect(formatted).toContain('test-session');
      expect(formatted).toContain('150');
      expect(formatted).toContain('test-model');
      expect(formatted).toContain('test-tool');
      expect(formatted).toContain('100.0%');
    });
  });
});
