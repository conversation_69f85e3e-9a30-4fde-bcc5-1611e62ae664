import { tool } from "@langchain/core/tools";
import { z } from "zod";
import { fetchWithTimeout, isPrivateIp } from '../utils/fetch.js';
import { convert } from 'html-to-text';
import { ProxyAgent, setGlobalDispatcher } from 'undici';
import { ChatOpenAI } from "@langchain/openai";
import { HumanMessage } from "@langchain/core/messages";
import { DEFAULT_OPENAI_CONFIGS } from '../config/models.js';

const URL_FETCH_TIMEOUT_MS = 10000;
const MAX_CONTENT_LENGTH = 100000;

// Helper function to extract URLs from a string
function extractUrls(text: string): string[] {
  const urlRegex = /(https?:\/\/[^\s]+)/g;
  return text.match(urlRegex) || [];
}

// Note: These interfaces are kept for future compatibility with grounding metadata
// Currently not used in LangChain implementation but may be needed for advanced features

/**
 * Initialize proxy configuration if available
 * Similar to packages/core/src/tools/web-fetch.ts proxy setup
 */
function initializeProxy(): void {
  const proxy = process.env.HTTP_PROXY || process.env.HTTPS_PROXY;
  if (proxy) {
    setGlobalDispatcher(new ProxyAgent(proxy as string));
  }
}

/**
 * Create a ChatOpenAI model instance using current configuration
 * TODO: 使用项目内的统一配置
 */
function createChatModel(): ChatOpenAI {
  return new ChatOpenAI(DEFAULT_OPENAI_CONFIGS);
}

// Initialize proxy on module load
initializeProxy();

/**
 * Fallback implementation for fetching and processing web content
 * Similar to executeFallback in packages/core/src/tools/web-fetch.ts
 */
async function executeFallback(prompt: string): Promise<string> {
  const urls = extractUrls(prompt);
  if (urls.length === 0) {
    throw new Error('No URL found in the prompt for fallback.');
  }

  // For now, we only support one URL for fallback
  let url = urls[0];

  // Convert GitHub blob URL to raw URL
  if (url.includes('github.com') && url.includes('/blob/')) {
    url = url
      .replace('github.com', 'raw.githubusercontent.com')
      .replace('/blob/', '/');
  }

  try {
    const response = await fetchWithTimeout(url, URL_FETCH_TIMEOUT_MS);
    if (!response.ok) {
      throw new Error(
        `Request failed with status code ${response.status} ${response.statusText}`,
      );
    }
    const html = await response.text();
    const textContent = convert(html, {
      wordwrap: false,
      selectors: [
        { selector: 'a', options: { ignoreHref: true } },
        { selector: 'img', format: 'skip' },
      ],
    }).substring(0, MAX_CONTENT_LENGTH);

    const chatModel = createChatModel();
    const fallbackPrompt = `The user requested the following: "${prompt}".

I was unable to access the URL directly. Instead, I have fetched the raw content of the page. Please use the following content to answer the user's request. Do not attempt to access the URL again.

---
${textContent}
---`;

    const result = await chatModel.invoke([new HumanMessage(fallbackPrompt)]);
    const resultText = typeof result.content === 'string' ? result.content : String(result.content);

    return `Content for ${url} processed using fallback fetch.\n\n${resultText}`;
  } catch (e) {
    const error = e as Error;
    const errorMessage = `Error during fallback fetch for ${url}: ${error.message}`;
    throw new Error(errorMessage);
  }
}

/**
 * WebFetch tool for langchain - processes content from URL(s) embedded in a prompt
 * Implements the same core logic as packages/core/src/tools/web-fetch.ts but follows langchainjs tool conventions
 */
export const webFetchTool = tool(
  async (params) => {
    const { prompt } = params;

    // Validation
    if (!prompt || prompt.trim() === '') {
      throw new Error("The 'prompt' parameter cannot be empty and must contain URL(s) and instructions.");
    }
    if (!prompt.includes('http://') && !prompt.includes('https://')) {
      throw new Error("The 'prompt' must contain at least one valid URL (starting with http:// or https://).");
    }

    const urls = extractUrls(prompt);
    const url = urls[0];
    const isPrivate = isPrivateIp(url);

    if (isPrivate) {
      return await executeFallback(prompt);
    }

    try {
      // Note: LangChain.js doesn't have direct equivalent to Gemini's urlContext tool
      // For now, we'll use the fallback approach for all URLs
      // In a real implementation, you might integrate with a web search API or web scraping service
      return await executeFallback(prompt);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`Error processing web content for prompt "${prompt.substring(0, 50)}...": ${errorMessage}`);
    }
  },
  {
    name: "web_fetch",
    description: "Processes content from URL(s), including local and private network addresses (e.g., localhost), embedded in a prompt. Include up to 20 URLs and instructions (e.g., summarize, extract specific data) directly in the 'prompt' parameter.",
    schema: z.object({
      prompt: z.string().describe("A comprehensive prompt that includes the URL(s) (up to 20) to fetch and specific instructions on how to process their content (e.g., \"Summarize https://example.com/article and extract key points from https://another.com/data\"). Must contain at least one URL starting with http:// or https://."),
    }),
  }
);
