import { tool } from "@langchain/core/tools";
import { z } from "zod";
import fs from 'fs';
import path from 'path';
import { isWithinRoot } from '../utils/fileUtils.js';
import { FileDiscoveryService } from '../services/fileDiscoveryService.js';

/**
 * Parameters for the LS tool
 */
export interface LSToolParams {
  /**
   * The absolute path to the directory to list
   */
  path: string;

  /**
   * Array of glob patterns to ignore (optional)
   */
  ignore?: string[] | null;

  /**
   * Whether to respect .gitignore and .haicodeignore patterns (optional, defaults to true)
   */
  file_filtering_options?: {
    respect_git_ignore?: boolean | null;
    respect_gemini_ignore?: boolean | null;
  } | null;
}

/**
 * File entry returned by LS tool
 */
export interface FileEntry {
  /**
   * Name of the file or directory
   */
  name: string;

  /**
   * Absolute path to the file or directory
   */
  path: string;

  /**
   * Whether this entry is a directory
   */
  isDirectory: boolean;

  /**
   * Size of the file in bytes (0 for directories)
   */
  size: number;

  /**
   * Last modified timestamp
   */
  modifiedTime: Date;
}

/**
 * Validates the parameters for the tool
 * @param params Parameters to validate
 * @param targetDir Target directory for validation
 * @returns An error message string if invalid, null otherwise
 */
function validateToolParams(params: LSToolParams, targetDir: string): string | null {
  if (!path.isAbsolute(params.path)) {
    return `Path must be absolute: ${params.path}`;
  }
  if (!isWithinRoot(params.path, targetDir)) {
    return `Path must be within the root directory (${targetDir}): ${params.path}`;
  }
  return null;
}

/**
 * Checks if a filename matches any of the ignore patterns
 * @param filename Filename to check
 * @param patterns Array of glob patterns to check against
 * @returns True if the filename should be ignored
 */
function shouldIgnore(filename: string, patterns?: string[] | null): boolean {
  if (!patterns || patterns.length === 0) {
    return false;
  }
  for (const pattern of patterns) {
    // Convert glob pattern to RegExp
    const regexPattern = pattern
      .replace(/[.+^${}()|[\]\\]/g, '\\$&')
      .replace(/\*/g, '.*')
      .replace(/\?/g, '.');
    const regex = new RegExp(`^${regexPattern}$`);
    if (regex.test(filename)) {
      return true;
    }
  }
  return false;
}

/**
 * LS tool for langchain - lists the names of files and subdirectories within a specified directory path
 * Implements the same core logic as packages/core/src/tools/ls.ts but follows langchainjs tool conventions
 */
export const lsTool = tool(
  async (params: LSToolParams) => {
    const targetDir = process.cwd();
    const validationError = validateToolParams(params, targetDir);
    if (validationError) {
      throw new Error(`Invalid parameters provided. Reason: ${validationError}`);
    }

    try {
      const stats = fs.statSync(params.path);
      if (!stats) {
        throw new Error(`Directory not found or inaccessible: ${params.path}`);
      }
      if (!stats.isDirectory()) {
        throw new Error(`Path is not a directory: ${params.path}`);
      }

      const files = fs.readdirSync(params.path);

      // Default file filtering options
      const fileFilteringOptions = {
        respectGitIgnore: params.file_filtering_options?.respect_git_ignore ?? true,
        respectGeminiIgnore: params.file_filtering_options?.respect_gemini_ignore ?? true,
      };

      // Get centralized file discovery service
      const fileDiscovery = new FileDiscoveryService(targetDir);

      const entries: FileEntry[] = [];
      let gitIgnoredCount = 0;
      let geminiIgnoredCount = 0;

      if (files.length === 0) {
        return `Directory ${params.path} is empty.`;
      }

      for (const file of files) {
        if (shouldIgnore(file, params.ignore)) {
          continue;
        }

        const fullPath = path.join(params.path, file);
        const relativePath = path.relative(targetDir, fullPath);

        // Check if this file should be ignored based on git or gemini ignore rules
        if (
          fileFilteringOptions.respectGitIgnore &&
          fileDiscovery.shouldGitIgnoreFile(relativePath)
        ) {
          gitIgnoredCount++;
          continue;
        }
        if (
          fileFilteringOptions.respectGeminiIgnore &&
          fileDiscovery.shouldGeminiIgnoreFile(relativePath)
        ) {
          geminiIgnoredCount++;
          continue;
        }

        try {
          const stats = fs.statSync(fullPath);
          const isDir = stats.isDirectory();
          entries.push({
            name: file,
            path: fullPath,
            isDirectory: isDir,
            size: isDir ? 0 : stats.size,
            modifiedTime: stats.mtime,
          });
        } catch (error) {
          // Log error internally but don't fail the whole listing
          console.error(`Error accessing ${fullPath}: ${error}`);
        }
      }

      // Sort entries (directories first, then alphabetically)
      entries.sort((a, b) => {
        if (a.isDirectory && !b.isDirectory) return -1;
        if (!a.isDirectory && b.isDirectory) return 1;
        return a.name.localeCompare(b.name);
      });

      // Create formatted content for LLM
      const directoryContent = entries
        .map((entry) => `${entry.isDirectory ? '[DIR] ' : ''}${entry.name}`)
        .join('\n');

      let resultMessage = `Directory listing for ${params.path}:\n${directoryContent}`;
      const ignoredMessages = [];
      if (gitIgnoredCount > 0) {
        ignoredMessages.push(`${gitIgnoredCount} git-ignored`);
      }
      if (geminiIgnoredCount > 0) {
        ignoredMessages.push(`${geminiIgnoredCount} gemini-ignored`);
      }

      if (ignoredMessages.length > 0) {
        resultMessage += `\n\n(${ignoredMessages.join(', ')})`;
      }

      return resultMessage;
    } catch (error) {
      const errorMsg = `Error listing directory: ${error instanceof Error ? error.message : String(error)}`;
      throw new Error(errorMsg);
    }
  },
  {
    name: "list_directory",
    description: "Lists the names of files and subdirectories directly within a specified directory path. Can optionally ignore entries matching provided glob patterns.",
    schema: z.object({
      path: z.string().describe(
        "The absolute path to the directory to list (must be absolute, not relative)"
      ),
      ignore: z.array(z.string()).nullable().optional().describe(
        "List of glob patterns to ignore"
      ),
      file_filtering_options: z.object({
        respect_git_ignore: z.boolean().nullable().optional().describe(
          "Optional: Whether to respect .gitignore patterns when listing files. Only available in git repositories. Defaults to true."
        ),
        respect_gemini_ignore: z.boolean().nullable().optional().describe(
          "Optional: Whether to respect .haicodeignore patterns when listing files. Defaults to true."
        ),
      }).nullable().optional().describe(
        "Optional: Whether to respect ignore patterns from .gitignore or .haicodeignore"
      ),
    }),
  }
);
