import { StateGraph, END } from "@langchain/langgraph";
import { Annotation } from "@langchain/langgraph";
import { AIMessage, SystemMessage, AIMessageChunk, HumanMessage, BaseMessage, isAIMessageChunk } from "@langchain/core/messages";
import type { DynamicStructuredTool } from "@langchain/core/tools";
import { ChatOpenAI } from "@langchain/openai";
import { ToolNode } from "@langchain/langgraph/prebuilt";
import { MemorySaver } from "@langchain/langgraph";
import type { BaseCheckpointSaver } from "@langchain/langgraph";
import { MongoDBSaver } from "@langchain/langgraph-checkpoint-mongodb";
import { MongoClient } from "mongodb";
import type { CallbackHandler } from "langfuse-langchain";
import { coreTools } from "./tools/index.js";
import { DEFAULT_OPENAI_CONFIGS } from "./config/models.js";
import { getLangfuseHelper } from "./utils/langfuseHelper.js";
import { getHaiAgentSystemPrompt } from "./prompt.js";
import type { HaiAgentMCPConfig } from "./config/mcp.js";
import { createMCPClient, type HaiAgentMCPClient } from "./mcp/client.js";
import { getUserInfo } from "./utils/user.js";
import { getUUID } from "./utils/uuid.js";
import { getVersion } from "./utils/version.js";
import { logger } from "./utils/logger.js";
import { TokenUsageTracker, DEFAULT_TOKEN_USAGE_CONFIG } from "./utils/tokenUsageTracker.js";
import type { TokenUsageConfig, SessionTokenStats } from "./types/tokenUsage.js";
import { formatSessionStats, formatAggregatedStats, formatCompactSessionSummary } from "./utils/statsFormatter.js";
import { CommandProcessor } from "./commands/processor.js";
import type { CommandResult } from "./commands/types.js";
import { CHECKPOINTER_CONFIG } from "./config/mongo.js";

// 1. Define the state
const StateAnnotation = Annotation.Root({
  messages: Annotation<BaseMessage[]>({
    reducer: (x, y) => x.concat(y),
  }),
});

type AgentStateType = typeof StateAnnotation.State;

export interface AgentConfig {
  model?: string;
  baseUrl?: string;
  apiKey?: string;
  temperature?: number;
  maxTokens?: number;
  // 系统提示词配置
  systemPrompt?: string;
  userMemory?: string;
  // Langfuse 配置
  langfuse?: {
    enabled?: boolean;
    userId?: string;
    sessionId?: string;
    release?: string;
    version?: string;
  };
  // MCP 配置
  mcp?: HaiAgentMCPConfig;
  // 会话持久化配置
  enablePersistence?: boolean;
  // MongoDB 持久化配置
  enableMongoPersistence?: boolean;
  maxIterations?: number;
  // Token 统计配置
  tokenUsage?: Partial<TokenUsageConfig>;
}

type ToolCallChunk = NonNullable<AIMessageChunk['tool_call_chunks']>[0];

// 会话信息接口
export interface SessionInfo {
  sessionId: string;
  createdAt: Date;
  updatedAt: Date;
  messageCount: number;
  lastMessage?: string;
}

// 会话详情接口
export interface SessionDetails {
  sessionId: string;
  createdAt: Date;
  updatedAt: Date;
  messages: BaseMessage[];
  messageCount: number;
}

export class HaiCodeAgent {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private compiledWorkflow: any;
  private config: AgentConfig;
  private langfuseHandler: CallbackHandler | null = null;
  private systemPromptCache: string | null = null;
  private mcpClient: HaiAgentMCPClient | null = null;
  private allTools: DynamicStructuredTool[] = [];
  private checkpointer?: BaseCheckpointSaver;
  private mongoClient?: MongoClient;
  private maxIterations: number = 25;
  private tokenUsageTracker!: TokenUsageTracker;
  private currentSessionId: string | null = null;
  private commandProcessor!: CommandProcessor;

  constructor(config: AgentConfig = {}) {
    this.config = config;
    this.maxIterations = config.maxIterations || 25;
    this.initializeLangfuse();
    this.initializeMCP();
    // initializeCheckpointer 现在是异步的，将在 ensureWorkflowInitialized 中调用
    this.initializeTokenUsage();
    this.initializeCommandProcessor();
    // 延迟初始化工作流，因为需要异步加载 MCP 工具和 checkpointer
    this.compiledWorkflow = null;
  }

  private initializeLangfuse(): void {
    const langfuseHelper = getLangfuseHelper();

    // 检查是否启用 Langfuse
    if (this.config.langfuse?.enabled !== false && langfuseHelper.isEnabled()) {
      this.langfuseHandler = langfuseHelper.createCallbackHandler({
        userId: this.config.langfuse?.userId || getUserInfo().userName,
        sessionId: this.config.langfuse?.sessionId || getUUID(),
        release: this.config.langfuse?.release,
        version: this.config.langfuse?.version || getVersion(),
      });
    }
  }

  private initializeMCP(): void {
    // 初始化 MCP 客户端
    if (this.config.mcp?.enabled) {
      this.mcpClient = createMCPClient(this.config.mcp);
    }
  }

  private async initializeCheckpointer(): Promise<void> {
    // 初始化 checkpointer 用于会话持久化
    if (this.config.enableMongoPersistence) {
      try {
        // 使用 MongoDB 持久化
        this.mongoClient = new MongoClient(CHECKPOINTER_CONFIG.uri);
        await this.mongoClient.connect();

        this.checkpointer = new MongoDBSaver({
          client: this.mongoClient,
          dbName: CHECKPOINTER_CONFIG.dbName,
          checkpointCollectionName: CHECKPOINTER_CONFIG.checkpointCollectionName,
          checkpointWritesCollectionName: CHECKPOINTER_CONFIG.checkpointWritesCollectionName,
        }) as unknown as BaseCheckpointSaver;

        logger.debug('[HaiCodeAgent] MongoDB checkpointer initialized for session persistence');
      } catch (error) {
        logger.error('[HaiCodeAgent] Failed to initialize MongoDB checkpointer:', { error: error instanceof Error ? error.message : String(error) });
        // 回退到内存存储
        this.checkpointer = new MemorySaver();
        logger.debug('[HaiCodeAgent] Fallback to MemorySaver checkpointer');
      }
    } else if (this.config.enablePersistence !== false) {
      // 使用内存持久化
      this.checkpointer = new MemorySaver();
      logger.debug('[HaiCodeAgent] MemorySaver checkpointer initialized for session persistence');
    }
  }

  private initializeTokenUsage(): void {
    // 初始化 token 使用统计
    const tokenUsageConfig = {
      ...DEFAULT_TOKEN_USAGE_CONFIG,
      ...this.config.tokenUsage,
    };

    this.tokenUsageTracker = new TokenUsageTracker(tokenUsageConfig);
    logger.debug('[HaiCodeAgent] Token usage tracker initialized', { config: tokenUsageConfig });
  }

  private initializeCommandProcessor(): void {
    // 初始化命令处理器
    this.commandProcessor = new CommandProcessor();
    logger.debug('[HaiCodeAgent] Command processor initialized');
  }

  private async initializeTools(): Promise<void> {
    // 开始使用核心工具
    this.allTools = [...coreTools];

    // 如果启用了 MCP，添加 MCP 工具
    if (this.mcpClient) {
      try {
        const mcpTools = await this.mcpClient.getTools();
        this.allTools.push(...mcpTools);
        logger.info(`添加了 ${mcpTools.length} 个 MCP 工具到代理`, { toolCount: mcpTools.length });
      } catch (error) {
        logger.warn('加载 MCP 工具失败', { error: error instanceof Error ? error.message : String(error) });
      }
    }
  }

  private async initializeWorkflow() {
    // 2. Set up the tools - combine core tools with MCP tools
    await this.initializeTools();
    const toolNode = new ToolNode(this.allTools);

    // 3. Set up the model with custom config
    const modelConfig = {
      ...DEFAULT_OPENAI_CONFIGS,
      modelName: this.config.model || DEFAULT_OPENAI_CONFIGS.modelName,
      apiKey: this.config.apiKey || DEFAULT_OPENAI_CONFIGS.apiKey,
      temperature: this.config.temperature,
      maxTokens: this.config.maxTokens,
      configuration: {
        ...DEFAULT_OPENAI_CONFIGS.configuration,
        baseURL: this.config.baseUrl || DEFAULT_OPENAI_CONFIGS.configuration.baseURL,
      },
    };

    const model = new ChatOpenAI(modelConfig);
    const boundModel = model.bindTools(this.allTools);

    // 4. Define the graph
    const routeMessage = (state: AgentStateType) => {
      const { messages } = state;
      const lastMessage = messages[messages.length - 1] as AIMessage;
      // If no tools are called, we can finish (respond to the user)
      if (!lastMessage?.tool_calls?.length) {
        return END;
      }
      // Otherwise if there is, we continue and call the tools
      return "tools";
    };

    const callModel = async (
      state: AgentStateType,
    ) => {
      // Following LangGraph.js best practices for system prompt handling
      // Based on the official documentation pattern
      const { messages } = state;

      // Get system prompt dynamically - this follows the recommended pattern
      // of building messages array with system prompt at the beginning
      const systemPrompt = this.getCurrentSystemPrompt();

      // Build messages array with system prompt at the beginning
      // This is the core LangGraph.js recommended pattern from the documentation
      const messagesWithSystem = [
        new SystemMessage(systemPrompt),
        ...messages
      ];

      const startTime = Date.now();
      const responseMessage = await boundModel.invoke(messagesWithSystem);
      const durationMs = Date.now() - startTime;

      // Extract and record token usage if available
      if (responseMessage.usage_metadata && this.tokenUsageTracker.getConfig().enabled) {
        const tokenUsage = this.extractTokenUsageFromMessage(responseMessage);
        if (tokenUsage) {
          // Try to get session ID from current context, fallback to default
          const sessionId = this.currentSessionId || 'default-session';
          const model = this.config.model || DEFAULT_OPENAI_CONFIGS.modelName;

          this.tokenUsageTracker.recordLLMCall(
            sessionId,
            model,
            tokenUsage,
            durationMs,
            true
          );
        }
      }

      return { messages: [responseMessage] };
    };

    const workflow = new StateGraph(StateAnnotation)
      .addNode("agent", callModel)
      .addNode("tools", toolNode)
      .addEdge("__start__", "agent")
      .addConditionalEdges("agent", routeMessage)
      .addEdge("tools", "agent");

    // 编译时添加 checkpointer 以支持会话持久化
    const compileOptions: { checkpointer?: BaseCheckpointSaver } = {};
    if (this.checkpointer) {
      compileOptions.checkpointer = this.checkpointer;
    }

    return workflow.compile(compileOptions);
  }

  private async ensureWorkflowInitialized(): Promise<void> {
    if (!this.compiledWorkflow) {
      // 确保 checkpointer 已初始化
      if (!this.checkpointer) {
        await this.initializeCheckpointer();
      }
      this.compiledWorkflow = await this.initializeWorkflow();
    }
  }

  async *streamMessage(message: string, sessionId?: string): AsyncGenerator<string, void, unknown> {
    // 生成或使用提供的 sessionId
    const threadId = sessionId || getUUID();

    // 设置当前会话ID
    this.currentSessionId = threadId;

    // 检查是否为命令
    if (this.commandProcessor.isCommand(message)) {
      try {
        const commandResult = await this.commandProcessor.processCommand(message, {
          sessionId: threadId,
          agent: this,
        });

        // 直接返回命令结果
        yield commandResult.content;
        return;
      } catch (error) {
        yield `❌ Command processing failed: ${error instanceof Error ? error.message : String(error)}`;
        return;
      }
    }

    // 确保工作流已初始化
    await this.ensureWorkflowInitialized();

    // 启动 token 统计会话（如果尚未启动）
    if (this.tokenUsageTracker.getConfig().enabled) {
      const existingSession = this.tokenUsageTracker.getSessionStats(threadId);
      if (!existingSession) {
        this.tokenUsageTracker.startSession(threadId);
      }
    }

    // 创建初始状态 - LangGraph MemorySaver 会自动合并现有线程状态
    const initialState = {
      messages: [new HumanMessage(message)],
      userMemory: this.config.userMemory || "",
      iterationCount: 0,
    };

    // 准备配置，包含 Langfuse 回调和 thread_id
    const config: Record<string, unknown> = {
      streamMode: "messages",
      recursionLimit: this.maxIterations,
      configurable: {
        thread_id: threadId, // 这是 MemorySaver 所需的
      },
    };

    // 添加回调处理器
    const callbacks = [];
    if (this.langfuseHandler) {
      callbacks.push(this.langfuseHandler);
    }

    if (callbacks.length > 0) {
      config.callbacks = callbacks;
    }

    const stream = await this.compiledWorkflow.stream(
      initialState,
      config,
    );

    for await (const [messageChunk, _metadata] of stream) {
      if (isAIMessageChunk(messageChunk) && messageChunk.tool_call_chunks?.length) {
        // For tool calls, we might want to show some indication but not the raw args
        yield* this.handleToolCallChunks(messageChunk.tool_call_chunks);
      } else if (messageChunk.content) {
        // TODO: 简化工具调用输出
        yield messageChunk.content;
      }
    }
  }

  /**
   * Handle tool call chunks for streaming tool call information
   */
  private async *handleToolCallChunks(toolCallChunks: ToolCallChunk[]): AsyncGenerator<string> {
    for (const chunk of toolCallChunks) {
      if (chunk.name) {
        yield `\n\n🔧 调用工具: ${chunk.name}\n`;
        if (chunk.args) {
          try {
            const args = typeof chunk.args === 'string' ? JSON.parse(chunk.args) : chunk.args;
            yield `\n📥 参数: ${JSON.stringify(args, null, 2)}\n`;
          } catch {
            yield `\n📥 参数: ${chunk.args}\n`;
          }
        }
      }
    }
  }

  async processMessage(message: string, sessionId?: string): Promise<string> {
    let fullResponse = '';
    for await (const chunk of this.streamMessage(message, sessionId)) {
      fullResponse += chunk;
    }
    return fullResponse;
  }

  /**
   * 获取当前会话状态
   */
  async getSessionState(sessionId: string): Promise<AgentStateType | null> {
    if (!this.checkpointer || !this.compiledWorkflow) {
      return null;
    }

    try {
      const state = await this.compiledWorkflow.getState({ 
        configurable: { thread_id: sessionId } 
      });
      return state?.values || null;
    } catch (error) {
      logger.debug('[HaiCodeAgent] Could not retrieve session state:', { error: error instanceof Error ? error.message : String(error) });
      return null;
    }
  }

  /**
   * 清除会话状态
   */
  async clearSessionState(sessionId: string): Promise<void> {
    if (!this.checkpointer || !this.compiledWorkflow) {
      return;
    }

    try {
      await this.compiledWorkflow.clearState({ 
        configurable: { thread_id: sessionId } 
      });
      logger.debug(`[HaiCodeAgent] Cleared session state for: ${sessionId}`);
    } catch (error) {
      logger.error('[HaiCodeAgent] Failed to clear session state:', { error: error instanceof Error ? error.message : String(error) });
    }
  }

  /**
   * 获取 Langfuse 配置状态
   */
  getLangfuseStatus(): { enabled: boolean; hasHandler: boolean } {
    return {
      enabled: getLangfuseHelper().isEnabled(),
      hasHandler: this.langfuseHandler !== null,
    };
  }

  /**
   * 刷新 Langfuse 事件（确保发送到服务器）
   */
  async flushLangfuse(): Promise<void> {
    if (this.langfuseHandler) {
      try {
        await this.langfuseHandler.flushAsync();
      } catch (error) {
        logger.error('刷新 Langfuse 事件失败', { error: error instanceof Error ? error.message : String(error) });
      }
    }
  }

  /**
   * 关闭 Langfuse 连接
   */
  async shutdownLangfuse(): Promise<void> {
    if (this.langfuseHandler) {
      try {
        await this.langfuseHandler.shutdownAsync();
      } catch (error) {
        logger.error('关闭 Langfuse 连接失败', { error: error instanceof Error ? error.message : String(error) });
      }
    }
  }

  /**
   * 更新系统提示词
   * 清除缓存以确保下次使用新的提示词
   */
  updateSystemPrompt(prompt: string): void {
    this.config.systemPrompt = prompt;
    this.systemPromptCache = null; // 清除缓存
    // 重新编译工作流以使用新的提示词
    this.compiledWorkflow = this.initializeWorkflow();
  }

  /**
   * 更新用户记忆
   * 清除缓存以确保下次使用新的记忆
   */
  updateUserMemory(memory: string): void {
    this.config.userMemory = memory;
    this.systemPromptCache = null; // 清除缓存
    // 重新编译工作流以使用新的记忆
    this.compiledWorkflow = this.initializeWorkflow();
  }

  /**
   * 获取当前系统提示词
   * 使用缓存提高性能，遵循 LangGraph.js 最佳实践
   */
  getCurrentSystemPrompt(): string {
    if (this.systemPromptCache) {
      return this.systemPromptCache;
    }

    let prompt: string;
    if (this.config.systemPrompt) {
      // 如果有自定义系统提示词，使用它作为基础
      // 根据 LangGraph 最佳实践，在自定义提示词中也要明确提及工具
      prompt = this.config.systemPrompt;

      // 如果有用户记忆，添加到提示词后面
      if (this.config.userMemory && this.config.userMemory.trim().length > 0) {
        prompt += `\n\n---\n\n${this.config.userMemory.trim()}`;
      }
    } else {
      // 使用默认系统提示词生成函数
      prompt = getHaiAgentSystemPrompt(this.config.model, this.config.userMemory);
    }

    this.systemPromptCache = prompt;
    return prompt;
  }

  /**
   * 重置为默认系统提示词
   */
  resetSystemPrompt(): void {
    this.config.systemPrompt = undefined;
    this.systemPromptCache = null; // 清除缓存
    this.compiledWorkflow = this.initializeWorkflow();
  }

  /**
   * 获取工具信息，用于系统提示词生成
   */
  getToolsInfo(): Array<{ name: string; description: string }> {
    return this.allTools.map(tool => ({
      name: tool.name,
      description: tool.description
    }));
  }

  /**
   * 获取 MCP 状态信息
   */
  getMCPStatus(): {
    enabled: boolean;
    initialized: boolean;
    serverCount: number;
    toolCount: number;
    servers: string[];
  } | null {
    return this.mcpClient ? this.mcpClient.getStatus() : null;
  }

  /**
   * 更新 MCP 配置
   */
  async updateMCPConfig(mcpConfig: HaiAgentMCPConfig): Promise<void> {
    this.config.mcp = mcpConfig;

    // 关闭现有 MCP 客户端
    if (this.mcpClient) {
      await this.mcpClient.close();
    }

    // 重新初始化 MCP
    this.initializeMCP();

    // 重新初始化工作流
    this.compiledWorkflow = null;
    await this.ensureWorkflowInitialized();
  }

  /**
   * 关闭 MCP 连接
   */
  async closeMCP(): Promise<void> {
    if (this.mcpClient) {
      await this.mcpClient.close();
      this.mcpClient = null;
    }
  }



  /**
   * 提取消息中的 token 使用信息
   */
  private extractTokenUsageFromMessage(message: AIMessage): import('./types/tokenUsage.js').TokenUsage | null {
    if (!message.usage_metadata) {
      return null;
    }

    const usageMeta = message.usage_metadata;
    // Safely access potentially undefined properties
    const extendedUsage = usageMeta as Record<string, unknown>;

    return {
      input_tokens: usageMeta.input_tokens || 0,
      output_tokens: usageMeta.output_tokens || 0,
      total_tokens: usageMeta.total_tokens || 0,
      cached_tokens: typeof extendedUsage.cached_tokens === 'number' ? extendedUsage.cached_tokens : undefined,
      reasoning_tokens: typeof extendedUsage.reasoning_tokens === 'number' ? extendedUsage.reasoning_tokens : undefined,
      tool_tokens: typeof extendedUsage.tool_tokens === 'number' ? extendedUsage.tool_tokens : undefined,
      input_token_details: typeof extendedUsage.input_token_details === 'object' ?
        extendedUsage.input_token_details as { audio?: number; cache_read?: number; cache_creation?: number } : undefined,
      output_token_details: typeof extendedUsage.output_token_details === 'object' ?
        extendedUsage.output_token_details as { audio?: number; reasoning?: number } : undefined,
    };
  }

  /**
   * 获取当前会话的 token 统计信息
   */
  getTokenUsageStats(sessionId?: string): SessionTokenStats | null {
    const targetSessionId = sessionId || 'default-session';
    return this.tokenUsageTracker.getSessionStats(targetSessionId);
  }

  /**
   * 获取所有会话的聚合统计信息
   */
  getAggregatedTokenStats(): ReturnType<TokenUsageTracker['getAggregatedStats']> {
    return this.tokenUsageTracker.getAggregatedStats();
  }

  /**
   * 清除指定会话的 token 统计信息
   */
  clearTokenUsageStats(sessionId?: string): void {
    if (sessionId) {
      this.tokenUsageTracker.clearSessionStats(sessionId);
    } else {
      this.tokenUsageTracker.clearAllStats();
    }
  }

  /**
   * 开始跟踪新会话的 token 使用
   */
  startTokenUsageSession(sessionId: string): void {
    this.tokenUsageTracker.startSession(sessionId);
  }

  /**
   * 结束会话的 token 使用跟踪
   */
  endTokenUsageSession(sessionId: string): void {
    this.tokenUsageTracker.endSession(sessionId);
  }

  /**
   * 获取格式化的会话统计信息
   */
  getFormattedSessionStats(sessionId?: string): string | null {
    const stats = this.getTokenUsageStats(sessionId);
    if (!stats) {
      return null;
    }
    return formatSessionStats(stats);
  }

  /**
   * 获取格式化的聚合统计信息
   */
  getFormattedAggregatedStats(): string {
    const stats = this.getAggregatedTokenStats();
    return formatAggregatedStats(stats);
  }

  /**
   * 获取紧凑格式的会话摘要
   */
  getCompactSessionSummary(sessionId?: string): string | null {
    const stats = this.getTokenUsageStats(sessionId);
    if (!stats) {
      return null;
    }
    return formatCompactSessionSummary(stats);
  }

  /**
   * 获取所有会话的紧凑摘要列表
   */
  getAllSessionSummaries(): string[] {
    const allStats = this.tokenUsageTracker.getAllSessionStats();
    return allStats.map(stats => formatCompactSessionSummary(stats));
  }

  /**
   * 获取最近的会话列表（最多10个）
   */
  async getRecentSessions(limit: number = 10): Promise<SessionInfo[]> {
    if (!this.mongoClient || !this.config.enableMongoPersistence) {
      logger.warn('[HaiCodeAgent] MongoDB persistence not enabled, cannot get recent sessions');
      return [];
    }

    try {
      const db = this.mongoClient.db(CHECKPOINTER_CONFIG.dbName);
      const collection = db.collection(CHECKPOINTER_CONFIG.checkpointCollectionName);

      // 获取所有不同的 thread_id，按最后更新时间排序
      const pipeline = [
        {
          $group: {
            _id: "$thread_id",
            createdAt: { $min: "$ts" },
            updatedAt: { $max: "$ts" },
            messageCount: { $sum: 1 }
          }
        },
        {
          $sort: { updatedAt: -1 }
        },
        {
          $limit: limit
        }
      ];

      const sessions = await collection.aggregate(pipeline).toArray();

      const sessionInfos: SessionInfo[] = [];

      for (const session of sessions) {
        // 获取最后一条消息
        const lastCheckpoint = await collection.findOne(
          { thread_id: session._id },
          { sort: { ts: -1 } }
        );

        let lastMessage = '';
        if (lastCheckpoint?.checkpoint?.channel_values?.messages) {
          const messages = lastCheckpoint.checkpoint.channel_values.messages;
          if (messages.length > 0) {
            const lastMsg = messages[messages.length - 1];
            if (lastMsg.content) {
              lastMessage = typeof lastMsg.content === 'string'
                ? lastMsg.content.substring(0, 100) + (lastMsg.content.length > 100 ? '...' : '')
                : JSON.stringify(lastMsg.content).substring(0, 100) + '...';
            }
          }
        }

        // 如果没有找到消息，尝试从其他字段获取
        if (!lastMessage && lastCheckpoint?.checkpoint) {
          try {
            const checkpointStr = JSON.stringify(lastCheckpoint.checkpoint);
            if (checkpointStr.includes('content')) {
              lastMessage = '有消息内容';
            }
          } catch {
            // 忽略错误
          }
        }

        sessionInfos.push({
          sessionId: session._id,
          createdAt: session.createdAt ? new Date(session.createdAt) : new Date(),
          updatedAt: session.updatedAt ? new Date(session.updatedAt) : new Date(),
          messageCount: session.messageCount,
          lastMessage
        });
      }

      return sessionInfos;
    } catch (error) {
      logger.error('[HaiCodeAgent] Failed to get recent sessions:', { error: error instanceof Error ? error.message : String(error) });
      return [];
    }
  }

  /**
   * 获取指定会话的详细信息
   */
  async getSessionDetails(sessionId: string): Promise<SessionDetails | null> {
    if (!this.mongoClient || !this.config.enableMongoPersistence) {
      logger.warn('[HaiCodeAgent] MongoDB persistence not enabled, cannot get session details');
      return null;
    }

    try {
      const db = this.mongoClient.db(CHECKPOINTER_CONFIG.dbName);
      const collection = db.collection(CHECKPOINTER_CONFIG.checkpointCollectionName);

      // 获取该会话的所有检查点，按时间排序
      const checkpoints = await collection.find(
        { thread_id: sessionId },
        { sort: { ts: 1 } }
      ).toArray();

      if (checkpoints.length === 0) {
        return null;
      }

      // 提取所有消息
      const allMessages: BaseMessage[] = [];
      const createdAt = checkpoints[0].ts ? new Date(checkpoints[0].ts) : new Date();
      const updatedAt = checkpoints[checkpoints.length - 1].ts ? new Date(checkpoints[checkpoints.length - 1].ts) : new Date();

      // 使用最新的检查点来获取完整的消息历史
      const latestCheckpoint = checkpoints[checkpoints.length - 1];
      if (latestCheckpoint?.checkpoint?.channel_values?.messages) {
        const messages = latestCheckpoint.checkpoint.channel_values.messages;
        allMessages.push(...messages);
      } else {
        // 如果最新检查点没有消息，尝试从所有检查点收集
        const messageSet = new Set<string>();
        for (const checkpoint of checkpoints) {
          if (checkpoint.checkpoint?.channel_values?.messages) {
            const messages = checkpoint.checkpoint.channel_values.messages;
            for (const msg of messages) {
              const msgKey = `${msg.constructor?.name || 'Message'}-${typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content)}`;
              if (!messageSet.has(msgKey)) {
                messageSet.add(msgKey);
                allMessages.push(msg);
              }
            }
          }
        }
      }

      return {
        sessionId,
        createdAt,
        updatedAt,
        messages: allMessages,
        messageCount: allMessages.length
      };
    } catch (error) {
      logger.error('[HaiCodeAgent] Failed to get session details:', { error: error instanceof Error ? error.message : String(error) });
      return null;
    }
  }

  /**
   * 关闭所有连接
   */
  async close(): Promise<void> {
    await Promise.all([
      this.flushLangfuse(),
      this.shutdownLangfuse(),
      this.closeMCP(),
      this.mongoClient?.close(),
    ]);
  }
}

// Export MCP functionality
export type {
  HaiAgentMCPConfig,
  MCPServerConfig,
  MCPServerStdioConfig,
  MCPServerHttpConfig,
} from './config/mcp.js';

export {
  PREDEFINED_MCP_SERVERS,
  DEFAULT_MCP_CONFIG,
  createMCPServerConfig,
  validateMCPServerConfig,
} from './config/mcp.js';

export {
  HaiAgentMCPClient,
  createMCPClient,
  testMCPServerConnection,
} from './mcp/client.js';

// Export token usage types and utilities
export type {
  TokenUsage,
  ModelTokenUsage,
  ToolCallStats,
  SessionTokenStats,
  TokenUsageEvent,
  TokenUsageConfig,
  TokenUsageCallback,
} from './types/tokenUsage.js';

export { TokenUsageTracker, DEFAULT_TOKEN_USAGE_CONFIG } from './utils/tokenUsageTracker.js';
export {
  formatSessionStats,
  formatAggregatedStats,
  formatCompactSessionSummary,
  formatDuration,
  formatNumber,
  formatPercentage,
} from './utils/statsFormatter.js';

// Export command system
export type {
  Command,
  CommandRegistry,
  CommandContext,
  CommandResult,
  ParsedCommand,
} from './commands/types.js';

export {
  isCommand,
  parseCommand,
  validateCommand,
  formatCommandHelp,
  formatCommandSpecificHelp,
} from './commands/parser.js';

export { DefaultCommandRegistry } from './commands/registry.js';
export { CommandProcessor } from './commands/processor.js';

// Export CLI formatters
export {
  formatCLISessionStats,
  formatCLIModelStats,
  formatCLIToolStats,
  formatCLICompactSummary,
} from './utils/cliFormatter.js';

