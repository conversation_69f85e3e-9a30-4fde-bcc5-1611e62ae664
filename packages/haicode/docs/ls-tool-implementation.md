# LS Tool Implementation

## 概述

本文档描述了 `packages/haicode/src/tools/ls.ts` 的实现，该实现基于 `packages/core/src/tools/ls.ts` 的核心逻辑，但遵循 langchainjs 的 tool 定义规范。

## 核心特性

### 1. 与 packages/core 的逻辑一致性

- **目录列表**: 实现了相同的目录列表功能，支持列出指定目录下的文件和子目录
- **参数验证**: 包含了相同的参数验证逻辑，确保路径为绝对路径且在根目录范围内
- **路径安全**: 实现了路径验证，防止访问根目录外的文件
- **文件过滤**: 支持 .gitignore 和 .haicodeignore 文件过滤
- **Glob 模式忽略**: 支持通过 glob 模式忽略特定文件
- **错误处理**: 遵循相同的错误处理模式

### 2. LangChain.js 兼容性

- **Tool 定义**: 使用 `tool()` 函数创建，符合 langchainjs 规范
- **Schema 验证**: 使用 Zod 进行参数 schema 定义
- **类型安全**: 完全类型化，避免使用 `any` 和 `unknown`
- **错误处理**: 遵循 langchainjs 的错误处理模式

## 主要组件

### 接口定义

```typescript
export interface LSToolParams {
  path: string;
  ignore?: string[] | null;
  file_filtering_options?: {
    respect_git_ignore?: boolean | null;
    respect_gemini_ignore?: boolean | null;
  } | null;
}

export interface FileEntry {
  name: string;
  path: string;
  isDirectory: boolean;
  size: number;
  modifiedTime: Date;
}
```

### 核心功能

1. **参数验证** (`validateToolParams`)
   - 验证路径为绝对路径
   - 确保路径在根目录范围内

2. **文件忽略** (`shouldIgnore`)
   - 支持 glob 模式匹配
   - 将 glob 模式转换为正则表达式

3. **文件发现服务集成**
   - 使用 `FileDiscoveryService` 进行 git 和 gemini ignore 过滤
   - 统计被忽略的文件数量

4. **目录列表**
   - 读取目录内容
   - 获取文件统计信息
   - 按目录优先、字母顺序排序

## 工具配置

```typescript
{
  name: "list_directory",
  description: "Lists the names of files and subdirectories directly within a specified directory path. Can optionally ignore entries matching provided glob patterns.",
  schema: z.object({
    path: z.string().describe("The absolute path to the directory to list (must be absolute, not relative)"),
    ignore: z.array(z.string()).nullable().optional().describe("List of glob patterns to ignore"),
    file_filtering_options: z.object({
      respect_git_ignore: z.boolean().nullable().optional().describe("Optional: Whether to respect .gitignore patterns when listing files. Only available in git repositories. Defaults to true."),
      respect_gemini_ignore: z.boolean().nullable().optional().describe("Optional: Whether to respect .haicodeignore patterns when listing files. Defaults to true."),
    }).nullable().optional().describe("Optional: Whether to respect ignore patterns from .gitignore or .haicodeignore"),
  }),
}
```

## 使用示例

### 基本用法

```typescript
import { lsTool } from './tools/ls.js';

// 列出当前目录
const result = await lsTool.invoke({
  path: '/absolute/path/to/directory'
});
```

### 带忽略模式

```typescript
// 忽略特定文件模式
const result = await lsTool.invoke({
  path: '/absolute/path/to/directory',
  ignore: ['*.log', 'node_modules', 'dist']
});
```

### 自定义文件过滤

```typescript
// 禁用 git 和 gemini ignore
const result = await lsTool.invoke({
  path: '/absolute/path/to/directory',
  file_filtering_options: {
    respect_git_ignore: false,
    respect_gemini_ignore: false
  }
});
```

## 输出格式

工具返回格式化的字符串，包含：

1. **目录路径**: 被列出的目录路径
2. **文件列表**: 按目录优先、字母顺序排序的文件和目录列表
3. **目录标识**: 目录前缀 `[DIR]` 标识
4. **忽略统计**: 显示被 git 或 gemini ignore 的文件数量

示例输出：
```
Directory listing for /path/to/directory:
[DIR] src
[DIR] docs
package.json
README.md
tsconfig.json

(5 git-ignored, 2 gemini-ignored)
```

## 错误处理

工具会在以下情况抛出错误：

1. **相对路径**: 当提供相对路径而非绝对路径时
2. **路径超出范围**: 当路径不在根目录范围内时
3. **目录不存在**: 当指定的目录不存在时
4. **非目录路径**: 当路径指向文件而非目录时
5. **访问权限**: 当没有权限访问目录时

## 集成

工具已集成到 `packages/haicode/src/tools/index.ts` 中：

- 导出为 `lsTool`
- 包含在 `allTools` 数组中
- 包含在 `coreTools` 数组中
- 包含在 `fileTools` 数组中

## 测试

实现包含了完整的测试覆盖：

1. **基本功能测试**: 验证目录列表功能
2. **忽略模式测试**: 验证 glob 模式忽略功能
3. **文件过滤测试**: 验证 git/gemini ignore 功能
4. **错误情况测试**: 验证各种错误情况的处理

所有测试都通过，确保实现的正确性和稳定性。
