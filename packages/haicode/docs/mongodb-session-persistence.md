# MongoDB 会话持久存储功能

HaiCodeAgent 现在支持使用 MongoDB 进行会话持久存储，允许您保存、检索和管理对话历史。

## 功能特性

- 🗄️ **MongoDB 持久存储**: 使用 MongoDB 保存会话数据，支持跨应用重启的持久化
- 📋 **会话列表管理**: 获取最近的会话列表，支持分页和排序
- 📖 **会话详情查看**: 查看指定会话的完整对话历史
- 🔄 **会话恢复**: 继续之前的对话，保持上下文连续性
- 🛡️ **自动回退**: 当 MongoDB 连接失败时自动回退到内存存储

## 配置要求

### MongoDB 配置

确保您的 MongoDB 服务正在运行，并且配置文件 `src/config/mongo.ts` 中的连接信息正确：

```typescript
export const CHECKPOINTER_CONFIG = {
  uri: '***************************************************************************',
  dbName: 'haicode_cli',
  checkpointCollectionName: 'haicode_agent_checkpoints',
  checkpointWritesCollectionName: 'haicode_agent_checkpoint_writes'
};
```

### 启用 MongoDB 持久存储

在创建 HaiCodeAgent 实例时，设置 `enableMongoPersistence: true`：

```typescript
import { HaiCodeAgent } from '@ht/hai-code-cli';

const agent = new HaiCodeAgent({
  enableMongoPersistence: true,  // 启用 MongoDB 持久存储
  systemPrompt: '你是一个智能助手',
  model: 'ht::saas-deepseek-v3',
  // 其他配置...
});
```

## API 使用方法

### 1. 获取最近的会话列表

```typescript
// 获取最近 10 个会话（默认）
const recentSessions = await agent.getRecentSessions();

// 获取最近 5 个会话
const recentSessions = await agent.getRecentSessions(5);

console.log('最近的会话:');
recentSessions.forEach(session => {
  console.log(`- ${session.sessionId}`);
  console.log(`  创建时间: ${session.createdAt.toLocaleString()}`);
  console.log(`  消息数量: ${session.messageCount}`);
  console.log(`  最后消息: ${session.lastMessage}`);
});
```

### 2. 获取会话详情

```typescript
const sessionDetails = await agent.getSessionDetails('your-session-id');

if (sessionDetails) {
  console.log(`会话 ${sessionDetails.sessionId} 详情:`);
  console.log(`消息总数: ${sessionDetails.messageCount}`);
  
  // 遍历所有消息
  sessionDetails.messages.forEach((message, index) => {
    const role = message.constructor.name === 'HumanMessage' ? '用户' : '助手';
    console.log(`${index + 1}. ${role}: ${message.content}`);
  });
}
```

### 3. 继续现有会话

```typescript
// 使用已存在的会话ID继续对话
const response = await agent.processMessage('继续我们之前的对话', 'existing-session-id');
console.log('Agent 回复:', response);
```

## 数据结构

### SessionInfo 接口

```typescript
interface SessionInfo {
  sessionId: string;      // 会话唯一标识
  createdAt: Date;        // 创建时间
  updatedAt: Date;        // 最后更新时间
  messageCount: number;   // 消息数量
  lastMessage?: string;   // 最后一条消息预览
}
```

### SessionDetails 接口

```typescript
interface SessionDetails {
  sessionId: string;      // 会话唯一标识
  createdAt: Date;        // 创建时间
  updatedAt: Date;        // 最后更新时间
  messages: BaseMessage[]; // 完整的消息历史
  messageCount: number;   // 消息数量
}
```

## 示例代码

### 完整示例

```typescript
import { HaiCodeAgent } from '@ht/hai-code-cli';

async function sessionManagementExample() {
  const agent = new HaiCodeAgent({
    enableMongoPersistence: true,
    systemPrompt: '你是一个智能助手',
  });

  try {
    // 1. 创建新会话
    const sessionId = `session-${Date.now()}`;
    await agent.processMessage('你好，我是新用户', sessionId);
    
    // 2. 获取会话列表
    const sessions = await agent.getRecentSessions(5);
    console.log(`找到 ${sessions.length} 个会话`);
    
    // 3. 查看会话详情
    if (sessions.length > 0) {
      const details = await agent.getSessionDetails(sessions[0].sessionId);
      console.log(`会话包含 ${details?.messageCount} 条消息`);
    }
    
    // 4. 继续会话
    const response = await agent.processMessage('请总结我们的对话', sessionId);
    console.log('总结:', response);
    
  } finally {
    await agent.close();
  }
}
```

## 测试和验证

项目包含了完整的测试文件来验证功能：

```bash
# 运行 MongoDB 持久存储测试
npx tsx examples/mongodb-session-test.ts

# 运行会话管理演示
npx tsx examples/session-management-demo.ts
```

## 故障排除

### 常见问题

1. **MongoDB 连接失败**
   - 检查 MongoDB 服务是否运行
   - 验证连接字符串和认证信息
   - 系统会自动回退到内存存储

2. **会话数据为空**
   - 确保 `enableMongoPersistence: true` 已设置
   - 检查 MongoDB 集合是否正确创建
   - 验证数据库权限

3. **时间戳显示异常**
   - 检查 MongoDB 中的时间戳字段格式
   - 确保系统时区设置正确

### 调试信息

启用调试日志查看详细信息：

```typescript
import { logger } from '@ht/hai-code-cli/utils/logger';

// 设置日志级别
logger.setLevel('debug');
```

## 性能考虑

- MongoDB 查询已优化，使用索引和聚合管道
- 会话列表查询限制返回数量，避免大量数据传输
- 消息内容在列表视图中被截断，详情视图显示完整内容
- 自动清理和归档机制可根据需要实现

## 安全注意事项

- 确保 MongoDB 连接使用适当的认证
- 考虑对敏感对话内容进行加密
- 定期备份会话数据
- 实施适当的访问控制和审计日志
